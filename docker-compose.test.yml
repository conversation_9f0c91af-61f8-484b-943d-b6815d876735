version: "3.8"

services:
  postgres-test:
    image: postgres:15-alpine
    container_name: gokeys-postgres-test
    environment:
      POSTGRES_DB: gokeys_test
      POSTGRES_USER: gokeys
      POSTGRES_PASSWORD: gokeys_test_password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
      - ./scripts/init-test-db.sql:/docker-entrypoint-initdb.d/init-test-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U gokeys -d gokeys_test"]
      interval: 5s
      timeout: 5s
      retries: 5
    networks:
      - gokeys-test

  valkey-test:
    image: valkey/valkey:7.2-alpine
    container_name: gokeys-valkey-test
    ports:
      - "6379:6379"
    command: valkey-server --appendonly yes --requirepass gokeys_test_password
    volumes:
      - valkey_test_data:/data
    healthcheck:
      test: ["CMD", "valkey-cli", "--raw", "incr", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5
    networks:
      - gokeys-test

volumes:
  postgres_test_data:
    driver: local
  valkey_test_data:
    driver: local

networks:
  gokeys-test:
    driver: bridge
