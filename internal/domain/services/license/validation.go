package license

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/gokeys/gokeys/internal/domain/services/crypto"
	"github.com/google/uuid"
)

// ValidationResult represents the result of license validation
type ValidationResult struct {
	Valid            bool                   `json:"valid"`
	License          *entities.License      `json:"license,omitempty"`
	Policy           *entities.Policy       `json:"policy,omitempty"`
	Account          *entities.Account      `json:"account,omitempty"`
	ValidationTime   time.Time              `json:"validation_time"`
	ExpiresAt        *time.Time             `json:"expires_at,omitempty"`
	MachinesUsed     int                    `json:"machines_used"`
	MachinesAllowed  int                    `json:"machines_allowed"`
	CoresUsed        int                    `json:"cores_used"`
	CoresAllowed     int                    `json:"cores_allowed"`
	ProcessesUsed    int                    `json:"processes_used"`
	ProcessesAllowed int                    `json:"processes_allowed"`
	UsersUsed        int                    `json:"users_used"`
	UsersAllowed     int                    `json:"users_allowed"`
	Claims           map[string]any `json:"claims,omitempty"`
	Errors           []string               `json:"errors,omitempty"`
	Warnings         []string               `json:"warnings,omitempty"`
	Detail           string                 `json:"detail,omitempty"`
	Code             string                 `json:"code,omitempty"`
}

// ValidationScope represents validation scope parameters (maps to Ruby scope validation)
type ValidationScope struct {
	Environment  *string  `json:"environment,omitempty"`
	Product      *string  `json:"product,omitempty"`
	Policy       *string  `json:"policy,omitempty"`
	User         *string  `json:"user,omitempty"`
	Machine      *string  `json:"machine,omitempty"`
	Fingerprint  *string  `json:"fingerprint,omitempty"`
	Fingerprints []string `json:"fingerprints,omitempty"`
	Components   []string `json:"components,omitempty"`
	Entitlements []string `json:"entitlements,omitempty"`
	Checksum     *string  `json:"checksum,omitempty"`
	Version      *string  `json:"version,omitempty"`
}

// ValidationOptions represents validation options
type ValidationOptions struct {
	Scope     *ValidationScope `json:"scope,omitempty"`
	SkipTouch bool             `json:"skip_touch,omitempty"`
}

// CacheInterface defines the caching interface
type CacheInterface interface {
	Set(ctx context.Context, key string, value any, expiration time.Duration) error
	Get(ctx context.Context, key string) (string, error)
	Delete(ctx context.Context, key string) error
	Exists(ctx context.Context, key string) (bool, error)
}

// ValidationService provides license validation with caching
type ValidationService struct {
	licenseRepo repositories.LicenseRepository
	accountRepo repositories.AccountRepository
	machineRepo repositories.MachineRepository
	policyRepo  repositories.PolicyRepository
	userRepo    repositories.UserRepository
	crypto      *crypto.CryptoService
	cache       CacheInterface
	cacheTTL    time.Duration
}

// NewValidationService creates a new validation service
func NewValidationService(
	licenseRepo repositories.LicenseRepository,
	accountRepo repositories.AccountRepository,
	machineRepo repositories.MachineRepository,
	policyRepo repositories.PolicyRepository,
	userRepo repositories.UserRepository,
	crypto *crypto.CryptoService,
	cache CacheInterface,
) *ValidationService {
	return &ValidationService{
		licenseRepo: licenseRepo,
		accountRepo: accountRepo,
		machineRepo: machineRepo,
		policyRepo:  policyRepo,
		userRepo:    userRepo,
		crypto:      crypto,
		cache:       cache,
		cacheTTL:    15 * time.Minute, // Default cache TTL
	}
}

// SetCacheTTL sets the cache time-to-live duration
func (vs *ValidationService) SetCacheTTL(ttl time.Duration) {
	vs.cacheTTL = ttl
}

// ValidateLicense validates a license key and returns detailed validation result
func (vs *ValidationService) ValidateLicense(ctx context.Context, licenseKey string, machineFingerprint *string, environment *string) (*ValidationResult, error) {
	// Build scope from legacy parameters
	scope := &ValidationScope{}
	if environment != nil {
		scope.Environment = environment
	}
	if machineFingerprint != nil {
		scope.Fingerprint = machineFingerprint
	}

	options := &ValidationOptions{
		Scope: scope,
	}

	return vs.ValidateLicenseWithOptions(ctx, licenseKey, options)
}

// ValidateLicenseWithOptions validates a license with full options (maps to Ruby LicenseValidationService)
func (vs *ValidationService) ValidateLicenseWithOptions(ctx context.Context, licenseKey string, options *ValidationOptions) (*ValidationResult, error) {
	result := &ValidationResult{
		ValidationTime: time.Now(),
		Errors:         []string{},
		Warnings:       []string{},
	}

	// Set default options
	if options == nil {
		options = &ValidationOptions{}
	}
	if options.Scope == nil {
		options.Scope = &ValidationScope{}
	}

	// Try to get from cache first
	cacheKey := vs.buildCacheKeyWithScope(licenseKey, options.Scope)
	if cachedResult, err := vs.getFromCache(ctx, cacheKey); err == nil && cachedResult != nil {
		return cachedResult, nil
	}

	// Step 1: Basic existence and format validation
	if licenseKey == "" {
		result.Detail = "does not exist"
		result.Code = "NOT_FOUND"
		return result, nil
	}

	// Get license from database
	license, err := vs.licenseRepo.GetByKey(ctx, licenseKey)
	if err != nil {
		result.Detail = "does not exist"
		result.Code = "NOT_FOUND"
		return result, nil
	}
	result.License = license

	// Get related entities
	accountUUID, err := uuid.Parse(license.AccountID)
	if err != nil {
		result.Detail = "invalid account ID format"
		result.Code = "INVALID_ACCOUNT"
		return result, nil
	}

	account, err := vs.accountRepo.GetByID(ctx, accountUUID)
	if err != nil {
		result.Detail = "account not found"
		result.Code = "ACCOUNT_NOT_FOUND"
		return result, nil
	}
	result.Account = account

	// Get policy
	if license.PolicyID != "" {
		policyUUID, err := uuid.Parse(license.PolicyID)
		if err == nil {
			policy, err := vs.policyRepo.GetByID(ctx, policyUUID)
			if err == nil {
				result.Policy = policy
			}
		}
	}

	// Core validation logic (mapping from Ruby LicenseValidationService)
	valid, detail, code := vs.validateCore(ctx, license, account, result.Policy, options.Scope)
	result.Valid = valid
	result.Detail = detail
	result.Code = code

	if !valid {
		return result, nil
	}

	// Populate usage statistics
	vs.populateUsageStats(ctx, result, license)

	// Touch license (update last_validated_at) unless skipped
	if !options.SkipTouch {
		vs.touchLicense(ctx, license, options.Scope)
	}

	// Cache the result
	if vs.cache != nil {
		if err := vs.cacheResult(ctx, cacheKey, result); err != nil {
			result.Warnings = append(result.Warnings, "Failed to cache validation result")
		}
	}

	return result, nil
}


// checkUserLicenseAssociation checks if user is associated with license
func (vs *ValidationService) checkUserLicenseAssociation(_ context.Context, _, _ string) bool {
	// TODO: Implement when license_users many-to-many table is available
	// Would query: SELECT 1 FROM license_users WHERE license_id = ? AND user_id = ?
	return false
}

// validateEntitlements checks if license has all required entitlements
func (vs *ValidationService) validateEntitlements(_ context.Context, _ *entities.License, _ []string) bool {
	// TODO: Implement when entitlements repository is available
	// Would query license.Entitlements and check if all required codes exist
	return true
}

// validateCore performs core validation logic mapping from Ruby LicenseValidationService.validate!
func (vs *ValidationService) validateCore(ctx context.Context, license *entities.License, account *entities.Account, policy *entities.Policy, scope *ValidationScope) (bool, string, string) {
	// Step 1: Check if license's user has been banned (matches Ruby logic)
	// Ruby: license.banned? checks if license.user&.banned_at? is set
	if license.UserID != nil {
		userUUID, err := uuid.Parse(*license.UserID)
		if err == nil {
			user, err := vs.userRepo.GetByID(ctx, userUUID)
			if err == nil && user != nil && user.BannedAt != nil && !user.BannedAt.IsZero() {
				return false, "is banned", "BANNED"
			}
		}
	}

	// Step 2: Check if license is suspended
	if license.Suspended {
		return false, "is suspended", "SUSPENDED"
	}

	// Step 3: Check if license is expired (higher precedence when revoking access)
	// Implement revoke_access logic based on policy expiration strategy
	if license.ExpiresAt != nil && time.Now().After(*license.ExpiresAt) {
		// Check policy expiration strategy for revoke_access behavior
		if policy != nil && policy.ExpirationStrategy != nil {
			switch *policy.ExpirationStrategy {
			case "REVOKE_ACCESS":
				// Immediately revoke access when expired (Ruby: license.revoke_access? && license.expired?)
				return false, "is expired", "EXPIRED"
			case "MAINTAIN_ACCESS":
				// Allow continued access even when expired (Ruby: !license.revoke_access?)
				// Continue validation but mark as expired
				break
			default:
				// Default behavior: revoke access when expired (Ruby: backwards compat)
				return false, "is expired", "EXPIRED"
			}
		} else {
			// No policy or strategy: default to revoking access
			return false, "is expired", "EXPIRED"
		}
	}

	// Step 4: Check if license is overdue for check in (Ruby: license.check_in_overdue?)
	if policy != nil && policy.RequireCheckIn {
		// Ruby logic: check_in_interval_count? && check_in_interval? && last_check_in_at? && requires_check_in?
		if license.LastCheckInAt != nil && policy.CheckInInterval != nil && policy.CheckInIntervalCount != nil {
			// Ruby: last_check_in_at < check_in_interval_count.send(check_in_interval).ago
			count := *policy.CheckInIntervalCount
			interval := *policy.CheckInInterval
			
			// Calculate duration based on Ruby's time helpers (only 4 valid intervals)
			var checkInDuration time.Duration
			switch interval {
			case "day":
				checkInDuration = time.Duration(count) * 24 * time.Hour
			case "week":
				checkInDuration = time.Duration(count) * 7 * 24 * time.Hour  
			case "month":
				// Approximate month as 30 days (Ruby uses similar approximation)
				checkInDuration = time.Duration(count) * 30 * 24 * time.Hour
			case "year":
				// Approximate year as 365 days
				checkInDuration = time.Duration(count) * 365 * 24 * time.Hour
			default:
				// Invalid interval, skip check (Ruby validation should prevent this)
				break
			}
			
			// Ruby: last_check_in_at < deadline_time_ago
			if checkInDuration > 0 && time.Since(*license.LastCheckInAt) > checkInDuration {
				return false, "is overdue for check in", "OVERDUE"
			}
		}
	}

	// Step 5: Scope validations (only if scope is not explicitly false)
	if scope != nil {
		if valid, detail, code := vs.validateScopes(ctx, license, account, policy, scope); !valid {
			return valid, detail, code
		}
	}

	// Step 6: Check if license has exceeded its user limit (matches Ruby logic exactly)
	if valid, detail, code := vs.validateUserLimitsWithOverage(ctx, license, policy); !valid {
		return valid, detail, code
	}

	// Step 7: Check if license policy is strict (Ruby logic)
	if policy != nil {
		// Check if policy is strict - if not strict, skip machine validations
		isStrict := policy.Strict

		// Ruby logic: unless license.policy.strict? (exit early if not strict)
		if !isStrict {
			// Check if license is expired after checking machine requirements (Ruby logic)
			if license.ExpiresAt != nil && time.Now().After(*license.ExpiresAt) {
				// Ruby: license.allow_access? || license.maintain_access?
				// Check policy expiration strategy
				if policy.ExpirationStrategy != nil && *policy.ExpirationStrategy == "MAINTAIN_ACCESS" {
					return true, "is valid", "VALID"
				}
				return false, "is expired", "EXPIRED"
			}
			return true, "is valid", "VALID"
		}

		// Step 8: Machine validation for strict policies (Ruby strict logic)
		if valid, detail, code := vs.validateStrictMachineRequirements(ctx, license, policy, scope); !valid {
			return valid, detail, code
		}
	}

	// Step 10: Check CPU core limits
	if valid, detail, code := vs.validateCoreLimits(ctx, license, policy); !valid {
		return valid, detail, code
	}

	// Step 11: Check process limits
	if valid, detail, code := vs.validateProcessLimits(ctx, license, policy, scope); !valid {
		return valid, detail, code
	}

	// Step 12: Final expiration check after machine requirements
	if license.ExpiresAt != nil && time.Now().After(*license.ExpiresAt) {
		// Ruby logic: license.allow_access? || license.maintain_access?
		// Check policy expiration strategy
		if policy != nil && policy.ExpirationStrategy != nil && *policy.ExpirationStrategy == "MAINTAIN_ACCESS" {
			return true, "is valid", "VALID"
		}
		return false, "is expired", "EXPIRED"
	}

	// All validations passed
	return true, "is valid", "VALID"
}

// validateScopes performs scope validation mapping from Ruby scope validation logic
func (vs *ValidationService) validateScopes(ctx context.Context, license *entities.License, _ *entities.Account, policy *entities.Policy, scope *ValidationScope) (bool, string, string) {
	// Environment scope validation (Ruby: license.environment&.code == scope[:environment] || license.environment&.id == scope[:environment])
	if scope.Environment != nil {
		// Check if license has environment and it matches the scope
		if license.EnvironmentID == nil {
			return false, "environment scope does not match", "ENVIRONMENT_SCOPE_MISMATCH"
		}
		// Ruby checks both environment.code and environment.id
		// For now, compare ID directly until EnvironmentRepository is implemented
		// TODO: Also check environment.code when EnvironmentRepository is available
		if *license.EnvironmentID != *scope.Environment {
			return false, "environment scope does not match", "ENVIRONMENT_SCOPE_MISMATCH"
		}
	} else if policy != nil && policy.RequireEnvironmentScope {
		return false, "environment scope is required", "ENVIRONMENT_SCOPE_REQUIRED"
	}

	// Product scope validation
	if scope.Product != nil {
		if license.ProductID != *scope.Product {
			return false, "product scope does not match", "PRODUCT_SCOPE_MISMATCH"
		}
	} else if policy != nil && policy.RequireProductScope {
		return false, "product scope is required", "PRODUCT_SCOPE_REQUIRED"
	}

	// Policy scope validation
	if scope.Policy != nil {
		if license.PolicyID != *scope.Policy {
			return false, "policy scope does not match", "POLICY_SCOPE_MISMATCH"
		}
	} else if policy != nil && policy.RequirePolicyScope {
		return false, "policy scope is required", "POLICY_SCOPE_REQUIRED"
	}

	// User scope validation (Ruby lines 77-90)
	if scope.User != nil {
		// Check if license owner matches or user is associated with license
		userMatches := false
		
		// Ruby: license.owner_id == user_identifier
		if license.UserID != nil && *license.UserID == *scope.User {
			userMatches = true
		} else {
			// Ruby: license.users.where(id: user_identifier).or(license.users.where(email: user_identifier)).exists?
			// Check if user is in license.users many-to-many relationship
			// First try to find by ID
			userMatches = vs.checkUserLicenseAssociation(ctx, license.ID, *scope.User)
			
			// If not found by ID, try to find by email
			if !userMatches && vs.userRepo != nil {
				// Try to find user by email in account
				accountUUID, err := uuid.Parse(license.AccountID)
				if err == nil {
					user, err := vs.userRepo.GetByEmail(ctx, *scope.User, accountUUID)
					if err == nil && user != nil {
						// Check if this user is associated with the license
						userMatches = vs.checkUserLicenseAssociation(ctx, license.ID, user.ID)
					}
				}
			}
		}
		if !userMatches {
			return false, "user scope does not match", "USER_SCOPE_MISMATCH"
		}
	} else if policy != nil && policy.RequireUserScope {
		return false, "user scope is required", "USER_SCOPE_REQUIRED"
	}

	// Entitlements scope validation
	if scope.Entitlements != nil {
		// Ruby logic: entitlements.empty? check
		if len(scope.Entitlements) == 0 {
			return false, "entitlements scope is empty", "ENTITLEMENTS_SCOPE_EMPTY"
		}
		
		// Ruby logic: license.entitlements.where(code: entitlements).count != entitlements.size
		if !vs.validateEntitlements(ctx, license, scope.Entitlements) {
			return false, "is missing one or more required entitlements", "ENTITLEMENTS_MISSING"
		}
	}

	// Machine scope validation (matches Ruby logic exactly)
	if scope.Machine != nil {
		licenseUUID, err := uuid.Parse(license.ID)
		if err != nil {
			return false, "invalid license ID", "INVALID_LICENSE"
		}

		machines, err := vs.machineRepo.GetByLicense(ctx, licenseUUID)
		if err != nil {
			return false, "failed to check machine scope", "DATABASE_ERROR"
		}

		machineCount := len(machines)

		// Ruby logic: Check floating vs node-locked behavior
		isFloating := policy != nil && policy.Floating

		// Check machine count requirements (matches Ruby case statements)
		switch {
		case !isFloating && machineCount == 0:
			return false, "machine is not activated (has no associated machine)", "NO_MACHINE"
		case isFloating && machineCount == 0:
			return false, "machine is not activated (has no associated machines)", "NO_MACHINES"
		default:
			// Find specific machine by ID
			var targetMachine *entities.Machine
			for _, machine := range machines {
				if machine.ID == *scope.Machine {
					targetMachine = machine
					break
				}
			}

			if targetMachine == nil {
				return false, "machine is not activated (does not match any associated machines)", "MACHINE_SCOPE_MISMATCH"
			}

			// Check user scope against machine owner (Ruby logic)
			if scope.User != nil {
				userMatches := targetMachine.OwnerID == nil ||
					(targetMachine.OwnerID != nil && *targetMachine.OwnerID == *scope.User)
				if !userMatches {
					return false, "user scope does not match (does not match associated machine owner)", "USER_SCOPE_MISMATCH"
				}
			}

			// Check heartbeat requirements (Ruby logic)
			if policy != nil && policy.RequireHeartbeat {
				if targetMachine.LastHeartbeatAt == nil {
					return false, "machine heartbeat is required", "HEARTBEAT_NOT_STARTED"
				}

				// Check if machine is dead (Ruby: machine.dead?)
				// For now, simple check - machine is dead if heartbeat is too old
				if policy.HeartbeatDuration != nil {
					heartbeatDuration := time.Duration(*policy.HeartbeatDuration) * time.Second
					if time.Since(*targetMachine.LastHeartbeatAt) > heartbeatDuration {
						return false, "machine heartbeat is dead", "HEARTBEAT_DEAD"
					}
				}
			}
		}
	} else if policy != nil && policy.RequireMachineScope {
		return false, "machine scope is required", "MACHINE_SCOPE_REQUIRED"
	}

	// Fingerprint scope validation (matches Ruby logic exactly)
	if scope.Fingerprint != nil || len(scope.Fingerprints) > 0 {
		licenseUUID, err := uuid.Parse(license.ID)
		if err != nil {
			return false, "invalid license ID", "INVALID_LICENSE"
		}

		machines, err := vs.machineRepo.GetByLicense(ctx, licenseUUID)
		if err != nil {
			return false, "failed to check fingerprint scope", "DATABASE_ERROR"
		}

		// Collect all fingerprints to check
		fingerprints := scope.Fingerprints
		if scope.Fingerprint != nil {
			fingerprints = append(fingerprints, *scope.Fingerprint)
		}

		if len(fingerprints) == 0 {
			return false, "fingerprint scope is empty", "FINGERPRINT_SCOPE_EMPTY"
		}

		machineCount := len(machines)
		isFloating := policy != nil && policy.Floating

		// Ruby logic: Check machine count requirements first
		switch {
		case !isFloating && machineCount == 0:
			return false, "fingerprint is not activated (has no associated machine)", "NO_MACHINE"
		case isFloating && machineCount == 0:
			return false, "fingerprint is not activated (has no associated machines)", "NO_MACHINES"
		default:
			// Find machines with matching fingerprints
			var matchingMachines []*entities.Machine
			var aliveMachines []*entities.Machine

			for _, machine := range machines {
				for _, fp := range fingerprints {
					if machine.Fingerprint == fp {
						matchingMachines = append(matchingMachines, machine)

						// Check if machine is alive (not dead from heartbeat)
						if !vs.isMachineDeadFromHeartbeat(machine, policy) {
							aliveMachines = append(aliveMachines, machine)
						}
						break
					}
				}
			}

			// Check if all machines are dead
			if len(matchingMachines) > 0 && len(aliveMachines) == 0 {
				return false, "machine heartbeat is dead", "HEARTBEAT_DEAD"
			}

			// Apply Ruby fingerprint matching strategies
			if len(fingerprints) > 1 && policy != nil {
				requiredMatches := vs.calculateRequiredMatches(len(fingerprints), policy)
				if len(aliveMachines) < requiredMatches {
					return false, "one or more fingerprint is not activated (does not match enough associated machines)", "FINGERPRINT_SCOPE_MISMATCH"
				}
			} else {
				// Single fingerprint or no specific strategy
				if len(aliveMachines) == 0 {
					return false, "fingerprint is not activated (does not match any associated machines)", "FINGERPRINT_SCOPE_MISMATCH"
				}
			}

			// Check user scope against machine owners (Ruby logic)
			if scope.User != nil {
				userMatches := true
				for _, machine := range aliveMachines {
					if machine.OwnerID != nil && *machine.OwnerID != *scope.User {
						userMatches = false
						break
					}
				}
				if !userMatches {
					return false, "user scope does not match (does not match associated machine owners)", "USER_SCOPE_MISMATCH"
				}
			}

			// Check heartbeat requirements for alive machines
			if policy != nil && policy.RequireHeartbeat {
				for _, machine := range aliveMachines {
					if machine.LastHeartbeatAt == nil {
						return false, "machine heartbeat is required", "HEARTBEAT_NOT_STARTED"
					}
				}
			}
		}
	} else if policy != nil && policy.RequireFingerprintScope {
		return false, "fingerprint scope is required", "FINGERPRINT_SCOPE_REQUIRED"
	}

	// Components scope validation (matches Ruby logic)
	if len(scope.Components) > 0 {
		// Ruby requires fingerprint scope when using components scope
		if scope.Fingerprint == nil {
			return false, "fingerprint scope is required when using the components scope", "FINGERPRINT_SCOPE_REQUIRED"
		}

		// Ruby logic: fingerprints = scope[:components].compact.uniq (lines 190-192)
		componentFingerprints := scope.Components
		if len(componentFingerprints) == 0 {
			return false, "components scope is empty", "COMPONENTS_SCOPE_EMPTY"
		}

		// Ruby logic: machine.components.with_fingerprint(fingerprints) (lines 198-200)
		// TODO: Implement when machine components repository is available
		// For now, we'll implement the matching strategy logic
		
		// Placeholder: assume we have component count
		componentCount := 0 // In real implementation: machine.components.with_fingerprint(fingerprints).count
		
		// Apply component matching strategies (Ruby lines 202-214)
		if policy != nil && policy.ComponentMatchingStrategy != nil {
			switch *policy.ComponentMatchingStrategy {
			case "match_most":
				requiredCount := (len(componentFingerprints) + 1) / 2 // Ceiling of half
				if componentCount < requiredCount {
					return false, "one or more component is not activated (does not match enough associated components)", "COMPONENTS_SCOPE_MISMATCH"
				}
			case "match_two":
				if componentCount < 2 {
					return false, "one or more component is not activated (does not match at least 2 associated components)", "COMPONENTS_SCOPE_MISMATCH"
				}
			case "match_all":
				if componentCount < len(componentFingerprints) {
					return false, "one or more component is not activated (does not match all associated components)", "COMPONENTS_SCOPE_MISMATCH"
				}
			default: // match_any or unspecified
				if componentCount == 0 {
					return false, "one or more component is not activated (does not match any associated components)", "COMPONENTS_SCOPE_MISMATCH"
				}
			}
		} else {
			// Default behavior: match_any
			if componentCount == 0 {
				return false, "one or more component is not activated (does not match any associated components)", "COMPONENTS_SCOPE_MISMATCH"
			}
		}
	} else if policy != nil && policy.RequireComponentsScope {
		return false, "components scope is required", "COMPONENTS_SCOPE_REQUIRED"
	}

	// Checksum scope validation (matches Ruby logic)
	if scope.Checksum != nil {
		// Ruby logic: product.release_artifacts.with_checksum(checksum).for_license(license).order_by_version.published.uploaded.take
		// This requires artifact repository and complex querying
		// For now, assume checksum is valid (placeholder)
		// In full implementation: check if checksum matches any accessible artifacts
	} else if policy != nil && policy.RequireChecksumScope {
		return false, "checksum scope is required", "CHECKSUM_SCOPE_REQUIRED"
	}

	// Version scope validation (matches Ruby logic)
	if scope.Version != nil {
		// Ruby logic: product.releases.with_version(version).for_license(license).published.take
		// This requires release repository and complex querying
		// For now, assume version is valid (placeholder)
		// In full implementation: check if version matches any accessible releases
	} else if policy != nil && policy.RequireVersionScope {
		return false, "version scope is required", "VERSION_SCOPE_REQUIRED"
	}

	return true, "", ""
}


// populateUsageStats populates usage statistics in validation result
func (vs *ValidationService) populateUsageStats(ctx context.Context, result *ValidationResult, license *entities.License) {
	licenseUUID, err := uuid.Parse(license.ID)
	if err != nil {
		return
	}

	// Get machines
	machines, err := vs.machineRepo.GetByLicense(ctx, licenseUUID)
	if err == nil {
		result.MachinesUsed = len(machines)

		// Count total cores
		totalCores := 0
		for _, machine := range machines {
			if machine.Cores > 0 {
				totalCores += machine.Cores
			}
		}
		result.CoresUsed = totalCores
	}

	// Set limits from policy or license overrides
	if result.Policy != nil {
		if result.Policy.MaxMachines != nil {
			result.MachinesAllowed = *result.Policy.MaxMachines
		}
		if result.Policy.MaxCores != nil {
			result.CoresAllowed = *result.Policy.MaxCores
		}
		// TODO: Set processes and users limits
	}

	// Apply license-specific overrides
	if license.MaxMachinesOverride != nil {
		result.MachinesAllowed = *license.MaxMachinesOverride
	}
	if license.MaxCoresOverride != nil {
		result.CoresAllowed = *license.MaxCoresOverride
	}
	// TODO: Apply other overrides
}

// touchLicense updates license timestamps (maps to Ruby TouchLicenseWorker)
func (vs *ValidationService) touchLicense(ctx context.Context, license *entities.License, scope *ValidationScope) {
	licenseUUID, err := uuid.Parse(license.ID)
	if err != nil {
		return
	}

	// Update last validated timestamp
	vs.licenseRepo.UpdateLastValidated(ctx, licenseUUID)

	// Update other touch fields based on scope (Ruby lines 233-234, 251)
	// Ruby logic: touches[:last_validated_checksum] = checksum
	if scope != nil && scope.Checksum != nil {
		// In Ruby, this is stored in touches hash and sent to TouchLicenseWorker
		// For Go, we would need to implement UpdateLastValidatedChecksum method
		// TODO: license.LastValidatedChecksum = scope.Checksum
		// TODO: vs.licenseRepo.UpdateLastValidatedChecksum(ctx, licenseUUID, *scope.Checksum)
	}
	
	// Ruby logic: touches[:last_validated_version] = version
	if scope != nil && scope.Version != nil {
		// In Ruby, this is stored in touches hash and sent to TouchLicenseWorker
		// For Go, we would need to implement UpdateLastValidatedVersion method
		// TODO: license.LastValidatedVersion = scope.Version
		// TODO: vs.licenseRepo.UpdateLastValidatedVersion(ctx, licenseUUID, *scope.Version)
	}
	
	// Note: In Ruby, this is handled asynchronously via TouchLicenseWorker.perform_async
	// In Go, we're doing synchronous updates for now
}

// buildCacheKeyWithScope builds cache key with scope parameters
func (vs *ValidationService) buildCacheKeyWithScope(licenseKey string, scope *ValidationScope) string {
	key := fmt.Sprintf("license_validation:%s", licenseKey)

	if scope != nil {
		if scope.Environment != nil {
			key += fmt.Sprintf(":env:%s", *scope.Environment)
		}
		if scope.Fingerprint != nil {
			key += fmt.Sprintf(":fp:%s", *scope.Fingerprint)
		}
		if scope.Machine != nil {
			key += fmt.Sprintf(":machine:%s", *scope.Machine)
		}
		if scope.User != nil {
			key += fmt.Sprintf(":user:%s", *scope.User)
		}
		// TODO: Add other scope parameters to cache key
	}

	return key
}

// validateLicenseFormat validates the license format and extracts claims
func (vs *ValidationService) validateLicenseFormat(licenseKey string) (map[string]any, error) {
	// Try to parse as JWT first
	header, claims, err := vs.crypto.JWT.ParseTokenWithoutVerification(licenseKey)
	if err == nil {
		// It's a JWT token
		claimsMap := make(map[string]any)
		claimsMap["format"] = "jwt"
		claimsMap["algorithm"] = header.Algorithm
		claimsMap["type"] = header.Type

		// Add all JWT claims
		for k, v := range claims {
			claimsMap[k] = v
		}

		return claimsMap, nil
	}

	// If not JWT, treat as simple license key
	if len(licenseKey) < 10 {
		return nil, fmt.Errorf("license key too short")
	}

	return map[string]any{
		"format": "simple",
		"length": len(licenseKey),
	}, nil
}



// getFromCache retrieves validation result from cache
func (vs *ValidationService) getFromCache(ctx context.Context, key string) (*ValidationResult, error) {
	if vs.cache == nil {
		return nil, fmt.Errorf("cache not configured")
	}

	exists, err := vs.cache.Exists(ctx, key)
	if err != nil || !exists {
		return nil, err
	}

	data, err := vs.cache.Get(ctx, key)
	if err != nil {
		return nil, err
	}

	var result ValidationResult
	if err := json.Unmarshal([]byte(data), &result); err != nil {
		return nil, err
	}

	// Check if cached result is still fresh
	if time.Since(result.ValidationTime) > vs.cacheTTL {
		vs.cache.Delete(ctx, key)
		return nil, fmt.Errorf("cached result expired")
	}

	return &result, nil
}

// cacheResult stores validation result in cache
func (vs *ValidationService) cacheResult(ctx context.Context, key string, result *ValidationResult) error {
	if vs.cache == nil {
		return fmt.Errorf("cache not configured")
	}

	data, err := json.Marshal(result)
	if err != nil {
		return err
	}

	return vs.cache.Set(ctx, key, string(data), vs.cacheTTL)
}

// InvalidateCache invalidates cache entries for a license
func (vs *ValidationService) InvalidateCache(ctx context.Context, licenseKey string) error {
	if vs.cache == nil {
		return nil
	}

	// Delete base cache key
	baseKey := fmt.Sprintf("license_validation:%s", licenseKey)
	return vs.cache.Delete(ctx, baseKey)
}

// ValidateLicenseQuick performs a quick validation without full database lookups
func (vs *ValidationService) ValidateLicenseQuick(ctx context.Context, licenseKey string) (bool, error) {
	// Check cache first
	cacheKey := vs.buildCacheKeyWithScope(licenseKey, nil)
	if cachedResult, err := vs.getFromCache(ctx, cacheKey); err == nil && cachedResult != nil {
		return cachedResult.Valid, nil
	}

	// Basic format validation
	_, err := vs.validateLicenseFormat(licenseKey)
	if err != nil {
		return false, err
	}

	// Check if license exists in database
	license, err := vs.licenseRepo.GetByKey(ctx, licenseKey)
	if err != nil {
		return false, nil
	}

	// Basic status check
	if license.Status != "active" {
		return false, nil
	}

	if license.Suspended {
		return false, nil
	}

	if license.ExpiresAt != nil && time.Now().After(*license.ExpiresAt) {
		return false, nil
	}

	return true, nil
}

// GetLicenseInfo returns basic license information without full validation
func (vs *ValidationService) GetLicenseInfo(ctx context.Context, licenseKey string) (map[string]any, error) {
	claims, err := vs.validateLicenseFormat(licenseKey)
	if err != nil {
		return nil, err
	}

	license, err := vs.licenseRepo.GetByKey(ctx, licenseKey)
	if err != nil {
		return claims, nil // Return format info even if license not found
	}

	info := make(map[string]any)
	for k, v := range claims {
		info[k] = v
	}

	info["status"] = license.Status
	info["account_id"] = license.AccountID
	info["product_id"] = license.ProductID

	if license.ExpiresAt != nil {
		info["expires_at"] = license.ExpiresAt
		info["expired"] = time.Now().After(*license.ExpiresAt)
	}

	info["suspended"] = license.Suspended

	return info, nil
}

// validateCoreLimits checks CPU core limits with overage handling
func (vs *ValidationService) validateCoreLimits(ctx context.Context, license *entities.License, policy *entities.Policy) (bool, string, string) {
	// Get effective core limit (license override or policy default)
	coreLimit, hasLimit := vs.getLicenseLimit(license.MaxCoresOverride, policy.MaxCores)
	if !hasLimit {
		return true, "", ""
	}

	licenseUUID, err := uuid.Parse(license.ID)
	if err != nil {
		return false, "invalid license ID", "INVALID_LICENSE"
	}

	machines, err := vs.machineRepo.GetByLicense(ctx, licenseUUID)
	if err != nil {
		return false, "failed to get machines", "DATABASE_ERROR"
	}

	// Count total cores used
	totalCores := 0
	for _, machine := range machines {
		if machine.Cores > 0 {
			totalCores += machine.Cores
		}
	}

	// Apply overage strategy using helper
	effectiveLimit := vs.getEffectiveLimitWithOverage(policy, coreLimit)
	if totalCores > effectiveLimit {
		return false, "has too many associated machine cores", "TOO_MANY_CORES"
	}

	return true, "", ""
}

// validateProcessLimits checks process limits with overage handling
func (vs *ValidationService) validateProcessLimits(_ context.Context, license *entities.License, policy *entities.Policy, scope *ValidationScope) (bool, string, string) {
	// Get effective process limit (license override or policy default)
	processLimit, hasLimit := vs.getLicenseLimit(license.MaxProcessesOverride, policy.MaxProcesses)
	if !hasLimit {
		return true, "", ""
	}
	
	var processCount int

	// Ruby logic: Check process leasing strategy
	if policy != nil && policy.ProcessLeasingStrategy != nil {
		switch *policy.ProcessLeasingStrategy {
		case "PER_MACHINE":
			// Ruby: process_lease_per_machine? logic (lines 372-384)
			if scope != nil && (scope.Fingerprint != nil || len(scope.Fingerprints) > 0) {
				// Find machine by fingerprint
				// fingerprint := ""
				// if scope.Fingerprint != nil {
				// 	fingerprint = *scope.Fingerprint
				// } else if len(scope.Fingerprints) > 0 {
				// 	fingerprint = scope.Fingerprints[0]
				// }
				
				// TODO: Get machine by fingerprint and count its processes
				// For now, we'll use placeholder logic
				// In full implementation:
				// machine = license.machines.alive.find_by(fingerprint: fingerprint)
				// processCount = machine.processes.count
				// processLimit = machine.max_processes
			} else if scope != nil && scope.Machine != nil {
				// Ruby: scope.key?(:machine) logic
				// TODO: Get machine by ID and count its processes
				// machine = license.machines.alive.find_by(id: scope[:machine])
				// processCount = machine.processes.count
				// processLimit = machine.max_processes
			}
			
		case "PER_LICENSE":
			// Ruby: process_lease_per_license? logic (lines 385-387)
			// processCount = license.processes.count
			// processLimit = license.max_processes
			// TODO: Implement when process repository is available
			
		case "PER_USER":
			// Ruby: process_lease_per_user? logic (lines 388-400)
			if scope != nil && scope.User != nil {
				// TODO: Count processes for specific user
				// owner = license.users.where(id: scope[:user]).or(license.users.where(email: scope[:user])).take
				// processCount = license.processes.left_outer_joins(:owner).where(owner: { id: owner }).count
			}
		}
	}

	// Apply overage logic using helper
	if processCount > processLimit {
		allowOverage := vs.checkOverageAllowed(policy, processCount, processLimit)
		return allowOverage, "has too many associated processes", "TOO_MANY_PROCESSES"
	}

	return true, "", ""
}

// validateUserLimitsWithOverage checks user limits with Ruby overage logic
func (vs *ValidationService) validateUserLimitsWithOverage(_ context.Context, license *entities.License, policy *entities.Policy) (bool, string, string) {
	// Get effective user limit (license override or policy default)
	userLimit, hasLimit := vs.getLicenseLimit(license.MaxUsersOverride, policy.MaxUsers)
	if !hasLimit {
		return true, "", ""
	}

	// Use cached count (Ruby: license.users_count)
	userCount := license.LicenseUsersCount
	if userCount == 0 {
		// TODO: Implement actual user counting from license_users table
		// For now, assume no users if count is 0
	}

	// Ruby logic: if license.users_count > license.max_users
	if userCount > userLimit {
		// Check overage using reusable helper
		allowOverage := vs.checkOverageAllowed(policy, userCount, userLimit)
		return allowOverage, "has too many associated users", "TOO_MANY_USERS"
	}

	return true, "", ""
}

// validateStrictMachineRequirements implements Ruby strict machine validation logic
func (vs *ValidationService) validateStrictMachineRequirements(ctx context.Context, license *entities.License, policy *entities.Policy, scope *ValidationScope) (bool, string, string) {
	licenseUUID, err := uuid.Parse(license.ID)
	if err != nil {
		return false, "invalid license ID", "INVALID_LICENSE"
	}

	machines, err := vs.machineRepo.GetByLicense(ctx, licenseUUID)
	if err != nil {
		return false, "failed to get machines", "DATABASE_ERROR"
	}

	machineCount := len(machines)
	isFloating := policy.Floating

	// Ruby logic: Check if license policy allows floating and if not, should have single activation
	if !isFloating && machineCount == 0 {
		return false, "must have exactly 1 associated machine", "NO_MACHINE"
	}

	// Ruby logic: When node-locked, license's machine count should not surpass 1
	if !isFloating && machineCount > 1 {
		// Ruby: machine_limit = license.max_machines || 1
		machineLimit, _ := vs.getLicenseLimit(license.MaxMachinesOverride, policy.MaxMachines)
		if machineLimit == 0 {
			machineLimit = 1 // Default for node-locked
		}

		// Ruby logic: machine_lease_per_user? case handling
		actualMachineCount := machineCount
		if policy.MachineLeasingStrategy != nil && *policy.MachineLeasingStrategy == "PER_USER" && scope != nil && scope.User != nil {
			// Count machines for specific user
			userMachineCount := 0
			for _, machine := range machines {
				if machine.OwnerID != nil && *machine.OwnerID == *scope.User {
					userMachineCount++
				}
			}
			actualMachineCount = userMachineCount
		}

		// Ruby overage logic for node-locked: only allow 2x overage
		if actualMachineCount > machineLimit {
			// For node-locked, only ALWAYS_ALLOW_OVERAGE and ALLOW_2X_OVERAGE are supported
			allowOverage := false
			if policy.OverageStrategy != nil {
				switch *policy.OverageStrategy {
				case "ALWAYS_ALLOW_OVERAGE":
					allowOverage = true
				case "ALLOW_2X_OVERAGE":
					allowOverage = actualMachineCount <= machineLimit*2
				}
			}
			return allowOverage, "has too many associated machines", "TOO_MANY_MACHINES"
		}
	}

	// Ruby logic: When floating, license should have at least 1 activation
	if isFloating && machineCount == 0 {
		return false, "must have at least 1 associated machine", "NO_MACHINES"
	}

	// Ruby logic: When floating, license's machine count should not surpass what policy allows
	// Ruby: if license.floating? && license.max_machines? && license.machines_count > 1
	machineLimit, hasLimit := vs.getLicenseLimit(license.MaxMachinesOverride, policy.MaxMachines)
	if isFloating && hasLimit && machineCount > 1 {

		// Ruby logic: machine_lease_per_user? case handling
		actualMachineCount := machineCount
		if policy.MachineLeasingStrategy != nil && *policy.MachineLeasingStrategy == "PER_USER" && scope != nil && scope.User != nil {
			// Count machines for specific user
			userMachineCount := 0
			for _, machine := range machines {
				if machine.OwnerID != nil && *machine.OwnerID == *scope.User {
					userMachineCount++
				}
			}
			actualMachineCount = userMachineCount
		}

		// Ruby overage logic for floating: 1.25x, 1.5x, 2x
		if actualMachineCount > machineLimit {
			allowOverage := vs.checkOverageAllowed(policy, actualMachineCount, machineLimit)
			return allowOverage, "has too many associated machines", "TOO_MANY_MACHINES"
		}
	}

	return true, "", ""
}

// checkOverageAllowed validates if overage is allowed based on policy strategy
func (vs *ValidationService) checkOverageAllowed(policy *entities.Policy, currentCount, limit int) bool {
	if policy == nil || policy.OverageStrategy == nil {
		return false
	}

	switch *policy.OverageStrategy {
	case "ALWAYS_ALLOW_OVERAGE":
		return true
	case "ALLOW_1_25X_OVERAGE":
		return currentCount <= int(float64(limit)*1.25)
	case "ALLOW_1_5X_OVERAGE":
		return currentCount <= int(float64(limit)*1.5)
	case "ALLOW_2X_OVERAGE":
		return currentCount <= limit*2
	default:
		return false
	}
}

// getEffectiveLimitWithOverage calculates effective limit with overage strategy
func (vs *ValidationService) getEffectiveLimitWithOverage(policy *entities.Policy, baseLimit int) int {
	if policy == nil || policy.OverageStrategy == nil {
		return baseLimit
	}

	switch *policy.OverageStrategy {
	case "ALWAYS_ALLOW_OVERAGE":
		return 999999 // Very high limit for always allow
	case "ALLOW_1_25X_OVERAGE":
		return int(float64(baseLimit) * 1.25)
	case "ALLOW_1_5X_OVERAGE":
		return int(float64(baseLimit) * 1.5)
	case "ALLOW_2X_OVERAGE":
		return baseLimit * 2
	default:
		return baseLimit
	}
}

// getLicenseLimit gets license limit with override priority over policy
func (vs *ValidationService) getLicenseLimit(licenseOverride *int, policyLimit *int) (int, bool) {
	if licenseOverride != nil {
		return *licenseOverride, true
	}
	if policyLimit != nil {
		return *policyLimit, true
	}
	return 0, false
}


// isMachineDeadFromHeartbeat checks if machine is dead based on heartbeat timing
func (vs *ValidationService) isMachineDeadFromHeartbeat(machine *entities.Machine, policy *entities.Policy) bool {
	if machine.LastHeartbeatAt == nil {
		return false // Not started, not dead
	}

	if policy == nil || policy.HeartbeatDuration == nil {
		return false // No heartbeat policy
	}

	heartbeatDuration := time.Duration(*policy.HeartbeatDuration) * time.Second
	return time.Since(*machine.LastHeartbeatAt) > heartbeatDuration
}

// calculateRequiredMatches calculates required matches based on Ruby matching strategies
func (vs *ValidationService) calculateRequiredMatches(fingerprintCount int, policy *entities.Policy) int {
	if policy.MachineMatchingStrategy == nil {
		return 1 // Default: any match
	}

	switch *policy.MachineMatchingStrategy {
	case "match_most":
		return (fingerprintCount + 1) / 2 // Ceiling of half
	case "match_two":
		return 2
	case "match_all":
		return fingerprintCount
	case "match_any":
		return 1
	default:
		return 1
	}
}
