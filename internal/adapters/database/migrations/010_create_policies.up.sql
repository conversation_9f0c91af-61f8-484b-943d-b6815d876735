-- Create policies table (references accounts, products, environments)
CREATE TABLE policies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    environment_id UUID REFERENCES environments(id) ON DELETE SET NULL,
    
    -- Basic information
    name VARCHAR(255) NOT NULL,
    description TEXT,
    strict BOOLEAN DEFAULT FALSE,
    protected BOOLEAN DEFAULT FALSE,
    duration INTEGER, -- in seconds
    lock_version INTEGER DEFAULT 0,
    
    -- Behavior flags
    floating BOOLEAN DEFAULT FALSE,
    use_pool BO<PERSON>EAN DEFAULT FALSE,
    encrypted BOOLEAN DEFAULT FALSE,
    concurrent BOOLEAN DEFAULT FALSE,
    
    -- Cryptographic scheme
    scheme VARCHAR(50) DEFAULT 'ED25519_SIGN', -- ED25519_SIGN, RSA_2048_PKCS1_SIGN, etc.
    
    -- Limits
    max_machines INTEGER,
    max_uses INTEGER,
    max_cores INTEGER,
    max_users INTEGER,
    max_processes INTEGER,
    max_activations INTEGER,
    max_deactivations INTEGER,
    
    -- Heartbeat configuration
    require_heartbeat BOOLEAN DEFAULT FALSE,
    heartbeat_duration INTEGER, -- in seconds
    heartbeat_basis VARCHAR(50) DEFAULT 'from_first_ping', -- from_first_ping, from_first_validation
    heartbeat_cull_strategy VARCHAR(50) DEFAULT 'deactivate_dead', -- deactivate_dead, keep_dead
    heartbeat_resurrection_strategy VARCHAR(50) DEFAULT 'no_revive', -- no_revive, revive_dead
    heartbeat_ping_strategy VARCHAR(50) DEFAULT 'per_machine', -- per_machine, per_license
    
    -- Check-in configuration  
    require_check_in BOOLEAN DEFAULT FALSE,
    check_in_interval VARCHAR(50) DEFAULT 'day', -- day, week, month, year
    check_in_interval_count INTEGER DEFAULT 1,
    
    -- Strategy configurations
    machine_uniqueness_strategy VARCHAR(50) DEFAULT 'unique_per_license', -- unique_per_license, unique_per_account, unique_per_product
    machine_matching_strategy VARCHAR(50) DEFAULT 'match_any', -- match_any, match_all, match_most
    component_uniqueness_strategy VARCHAR(50) DEFAULT 'unique_per_machine', -- unique_per_machine, unique_per_license
    component_matching_strategy VARCHAR(50) DEFAULT 'match_any', -- match_any, match_all, match_most
    process_uniqueness_strategy VARCHAR(50) DEFAULT 'unique_per_machine', -- unique_per_machine, unique_per_license
    process_matching_strategy VARCHAR(50) DEFAULT 'match_any', -- match_any, match_all, match_most
    fingerprint_uniqueness_strategy VARCHAR(50) DEFAULT 'unique_per_license', -- unique_per_license, unique_per_account
    fingerprint_matching_strategy VARCHAR(50) DEFAULT 'exact', -- exact, fuzzy, loose
    expiration_strategy VARCHAR(50) DEFAULT 'restrict_access', -- restrict_access, revoke_access, maintain_access
    expiration_basis VARCHAR(50) DEFAULT 'from_creation', -- from_creation, from_first_validation, from_first_activation
    renewal_basis VARCHAR(50) DEFAULT 'from_expiry', -- from_expiry, from_now
    authentication_strategy VARCHAR(50) DEFAULT 'token', -- token, license, mixed, user
    machine_leasing_strategy VARCHAR(50) DEFAULT 'per_license', -- per_license, per_user
    process_leasing_strategy VARCHAR(50) DEFAULT 'per_machine', -- per_machine, per_license
    overage_strategy VARCHAR(50) DEFAULT 'no_overage', -- no_overage, allow_1_25x_overage, allow_1_5x_overage, allow_2x_overage, always_allow_overage
    transfer_strategy VARCHAR(50) DEFAULT 'reset_expiry', -- reset_expiry, keep_expiry
    
    -- Scope requirements
    require_fingerprint_scope BOOLEAN DEFAULT FALSE,
    require_machine_scope BOOLEAN DEFAULT TRUE,
    require_entitlement_scope BOOLEAN DEFAULT FALSE,
    require_check_in_scope BOOLEAN DEFAULT FALSE,
    require_check_out_scope BOOLEAN DEFAULT FALSE,
    require_environment_scope BOOLEAN DEFAULT FALSE,
    require_product_scope BOOLEAN DEFAULT FALSE,
    require_policy_scope BOOLEAN DEFAULT FALSE,
    require_user_scope BOOLEAN DEFAULT FALSE,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Indexes for policies
CREATE INDEX idx_policies_account_id ON policies(account_id);
CREATE INDEX idx_policies_product_id ON policies(product_id);
CREATE INDEX idx_policies_environment_id ON policies(environment_id);
CREATE INDEX idx_policies_strict ON policies(strict);
CREATE INDEX idx_policies_protected ON policies(protected);
CREATE INDEX idx_policies_scheme ON policies(scheme);
CREATE INDEX idx_policies_require_heartbeat ON policies(require_heartbeat);
CREATE INDEX idx_policies_require_check_in ON policies(require_check_in);
CREATE INDEX idx_policies_machine_uniqueness_strategy ON policies(machine_uniqueness_strategy);
CREATE INDEX idx_policies_fingerprint_uniqueness_strategy ON policies(fingerprint_uniqueness_strategy);
CREATE INDEX idx_policies_deleted_at ON policies(deleted_at);
CREATE INDEX idx_policies_created_at ON policies(created_at);
CREATE INDEX idx_policies_metadata_gin ON policies USING gin(metadata);