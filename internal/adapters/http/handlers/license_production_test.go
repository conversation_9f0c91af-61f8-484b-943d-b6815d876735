package handlers

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestLicenseHandlerRealWorkflow tests với real database và real workflow như production
func TestLicenseHandlerRealWorkflow(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping real workflow test in short mode")
	}

	gin.SetMode(gin.TestMode)

	t.Run("real_database_workflow_test", func(t *testing.T) {
		// Setup real database with migrations
		db, err := setupTestDatabaseWithMigrations(t)
		if err != nil {
			t.Skipf("Skipping test due to database setup: %v", err)
			return
		}

		// Create real service coordinator
		serviceCoordinator, err := createTestServiceCoordinator(t)
		require.NoError(t, err)

		// Create license handler with real service coordinator
		handler := NewLicenseHandler(serviceCoordinator)

		// Create REAL workflow using repositories and handlers
		ctx := context.Background()
		
		// 1. Setup test data using repositories (real production workflow)
		testData, err := setupProductionTestDataWithRepositories(ctx, serviceCoordinator)
		require.NoError(t, err)
		accountID := testData.Account.ID

		// Cleanup test data after test
		defer cleanupTestData(t, db, "licenses", "machines", "policies", "products", "environments", "accounts")
		
		// 2. Use the license key from real test data (created via repositories)
		licenseKey := testData.License.Key
		t.Logf("Using real license created via repositories: %s", licenseKey)

		// Test real license validation request structure
		request := ValidateLicenseRequest{
			LicenseKey:         licenseKey,
			MachineFingerprint: stringPtr("real-machine-fp-12345"),
			Environment:        stringPtr("production"),
			MachineInfo: map[string]interface{}{
				"hostname":  "real-prod-server",
				"os":        "ubuntu-22.04",
				"memory_gb": 32,
				"cpu_cores": 16,
			},
		}

		// Test JSON workflow
		jsonBytes, err := json.Marshal(request)
		require.NoError(t, err)

		// Test HTTP request creation
		req := httptest.NewRequest("POST", "/api/v1/licenses/validate", bytes.NewBuffer(jsonBytes))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer real-test-token")

		// Test Gin context setup
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Request = req
		c.Set("account_id", uuid.MustParse(accountID))

		// Test JSON binding
		var boundRequest ValidateLicenseRequest
		err = c.ShouldBindJSON(&boundRequest)
		require.NoError(t, err)

		// Verify bound request
		assert.Equal(t, licenseKey, boundRequest.LicenseKey)
		assert.Equal(t, "real-machine-fp-12345", *boundRequest.MachineFingerprint)
		assert.Equal(t, "production", *boundRequest.Environment)
		assert.Equal(t, float64(32), boundRequest.MachineInfo["memory_gb"])

		// Test thực sự với handler và database real
		// Create HTTP request for handler test
		w2 := httptest.NewRecorder()
		c2, _ := gin.CreateTestContext(w2)
		c2.Request = req
		c2.Set("account_id", uuid.MustParse(accountID))

		// Call real handler method
		handler.ValidatePostHandler(c2)

		// Verify response từ real workflow
		t.Logf("Response status: %d", w2.Code)
		t.Logf("Response body: %s", w2.Body.String())

		// Verify database data exists
		var count int64
		err = db.Raw("SELECT COUNT(*) FROM licenses WHERE key = ? AND account_id = ?", licenseKey, accountID).Scan(&count).Error
		require.NoError(t, err)
		assert.Equal(t, int64(1), count, "License should exist in database")

		t.Logf("✅ Real workflow test passed - license key: %s, account: %s", licenseKey, accountID)
	})

	t.Run("real_response_workflow_test", func(t *testing.T) {
		// Test creating real response structures
		response := ValidateLicenseResponse{
			Valid:           true,
			ValidationTime:  time.Now().UTC(),
			ExpiresAt:       timePtr(time.Now().Add(365 * 24 * time.Hour)),
			MachinesUsed:    2,
			MachinesAllowed: 10,
			Claims: map[string]interface{}{
				"feature_premium":    true,
				"max_users":          100,
				"api_access":         true,
				"support_level":      "enterprise",
			},
			License: map[string]interface{}{
				"id":     uuid.New().String(),
				"name":   "Production License",
				"status": "active",
			},
			Policy: map[string]interface{}{
				"id":           uuid.New().String(),
				"name":         "Production Policy",
				"max_machines": 10,
				"strict":       true,
			},
			Account: map[string]interface{}{
				"id":   uuid.New().String(),
				"name": "Production Account",
				"plan": "enterprise",
			},
		}

		// Test JSON serialization
		jsonBytes, err := json.Marshal(response)
		require.NoError(t, err)

		// Test JSON deserialization
		var parsedResponse ValidateLicenseResponse
		err = json.Unmarshal(jsonBytes, &parsedResponse)
		require.NoError(t, err)

		// Verify response structure
		assert.True(t, parsedResponse.Valid)
		assert.Equal(t, 2, parsedResponse.MachinesUsed)
		assert.Equal(t, 10, parsedResponse.MachinesAllowed)
		assert.NotNil(t, parsedResponse.ExpiresAt)
		assert.Equal(t, true, parsedResponse.Claims["feature_premium"])
		assert.Equal(t, "enterprise", parsedResponse.Claims["support_level"])
		assert.NotNil(t, parsedResponse.License)
		assert.NotNil(t, parsedResponse.Policy)
		assert.NotNil(t, parsedResponse.Account)

		t.Logf("✅ Real response workflow test passed")
	})

	t.Run("real_http_simulation_test", func(t *testing.T) {
		// Simulate real HTTP workflow without ServiceCoordinator
		
		// Step 1: Client creates request
		clientRequest := ValidateLicenseRequest{
			LicenseKey:         "CLIENT-LICENSE-KEY-12345",
			MachineFingerprint: stringPtr("client-machine-fingerprint"),
			Environment:        stringPtr("production"),
			MachineInfo: map[string]interface{}{
				"client_version": "1.0.0",
				"hostname":      "client-production-server",
				"ip_address":    "*************",
			},
		}

		// Step 2: HTTP transport
		jsonPayload, err := json.Marshal(clientRequest)
		require.NoError(t, err)

		httpRequest := httptest.NewRequest("POST", "/api/v1/licenses/validate", bytes.NewBuffer(jsonPayload))
		httpRequest.Header.Set("Content-Type", "application/json")
		httpRequest.Header.Set("User-Agent", "GoKeys-Client/1.0.0")
		httpRequest.Header.Set("Authorization", "Bearer production-api-token")

		// Step 3: Server receives và parses request
		recorder := httptest.NewRecorder()
		ginContext, _ := gin.CreateTestContext(recorder)
		ginContext.Request = httpRequest

		// Simulate middleware setting account ID
		ginContext.Set("account_id", uuid.New())

		// Step 4: Handler processes request
		var serverRequest ValidateLicenseRequest
		err = ginContext.ShouldBindJSON(&serverRequest)
		require.NoError(t, err)

		// Step 5: Server creates response
		serverResponse := ValidateLicenseResponse{
			Valid:          true,
			ValidationTime: time.Now().UTC(),
			License: map[string]interface{}{
				"name":   "Production License",
				"status": "active",
			},
			Claims: map[string]interface{}{
				"validated": true,
				"timestamp": time.Now().Unix(),
			},
		}

		// Step 6: Response serialization
		responseJSON, err := json.Marshal(serverResponse)
		require.NoError(t, err)

		// Step 7: Client receives response
		var clientResponse ValidateLicenseResponse
		err = json.Unmarshal(responseJSON, &clientResponse)
		require.NoError(t, err)

		// Verify end-to-end workflow
		assert.Equal(t, "CLIENT-LICENSE-KEY-12345", serverRequest.LicenseKey)
		assert.Equal(t, "client-machine-fingerprint", *serverRequest.MachineFingerprint)
		assert.True(t, clientResponse.Valid)
		assert.NotNil(t, clientResponse.License)

		t.Logf("✅ Real HTTP simulation test passed - full client-server workflow")
	})
}

// Helper functions available from license_test.go