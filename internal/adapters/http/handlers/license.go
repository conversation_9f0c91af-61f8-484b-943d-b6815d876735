package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/adapters/http/middleware"
	"github.com/gokeys/gokeys/internal/adapters/http/responses"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/gokeys/gokeys/internal/domain/services"
	"github.com/gokeys/gokeys/internal/domain/services/events"
	"github.com/google/uuid"
)

// LicenseHandler handles license-related HTTP requests
type LicenseHandler struct {
	serviceCoordinator *services.ServiceCoordinator
}

// NewLicenseHandler creates a new license handler
func NewLicenseHandler(serviceCoordinator *services.ServiceCoordinator) *LicenseHandler {
	return &LicenseHandler{
		serviceCoordinator: serviceCoordinator,
	}
}

// Go-style request structs - simple and clear
type LicenseCreateRequest struct {
	Name      string                 `json:"name" binding:"required,min=1,max=255"`
	Key       string                 `json:"key,omitempty"`
	PolicyID  string                 `json:"policy_id" binding:"required,uuid"`
	UserID    *string                `json:"user_id,omitempty" binding:"omitempty,uuid"`
	ExpiresAt *time.Time             `json:"expires_at,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

type LicenseUpdateRequest struct {
	Name      *string                `json:"name,omitempty" binding:"omitempty,min=1,max=255"`
	Status    *string                `json:"status,omitempty" binding:"omitempty,oneof=ACTIVE SUSPENDED EXPIRED"`
	ExpiresAt *time.Time             `json:"expires_at,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// ValidateLicenseRequest represents the license validation request
type ValidateLicenseRequest struct {
	LicenseKey         string                 `json:"license_key" binding:"required" example:"LIC-12345-ABCDE-67890-FGHIJ"`
	MachineFingerprint *string                `json:"machine_fingerprint,omitempty" example:"fp-mac-12345678"`
	Environment        *string                `json:"environment,omitempty" example:"production"`
	MachineInfo        map[string]interface{} `json:"machine_info,omitempty" example:"{\"hostname\":\"server-01\",\"os\":\"linux\"}"`
}

// ValidateLicenseResponse represents the license validation response
type ValidateLicenseResponse struct {
	Valid           bool                   `json:"valid" example:"true"`
	License         interface{}            `json:"license,omitempty"`
	Policy          interface{}            `json:"policy,omitempty"`
	Account         interface{}            `json:"account,omitempty"`
	ValidationTime  time.Time              `json:"validation_time" example:"2025-07-12T16:15:30Z"`
	ExpiresAt       *time.Time             `json:"expires_at,omitempty" example:"2025-12-31T23:59:59Z"`
	MachinesUsed    int                    `json:"machines_used" example:"2"`
	MachinesAllowed int                    `json:"machines_allowed" example:"5"`
	Claims          map[string]interface{} `json:"claims,omitempty"`
	Errors          []string               `json:"errors,omitempty"`
	Warnings        []string               `json:"warnings,omitempty"`
	CacheHit        bool                   `json:"cache_hit,omitempty" example:"false"`
}

// Go-style response structs
type LicenseResponse struct {
	ID        string                 `json:"id"`
	Name      string                 `json:"name"`
	Key       string                 `json:"key"`
	Status    string                 `json:"status"`
	PolicyID  string                 `json:"policy_id"`
	UserID    *string                `json:"user_id,omitempty"`
	ExpiresAt *string                `json:"expires_at,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
	Created   string                 `json:"created_at"`
	Updated   string                 `json:"updated_at"`
}

type LicenseListResponse struct {
	Licenses   []LicenseResponse `json:"licenses"`
	Pagination PaginationInfo    `json:"pagination"`
}

// PaginationInfo is defined in account.go - using that one

// Helper function to convert entity to response
func (lh *LicenseHandler) toLicenseResponse(license *entities.License) LicenseResponse {
	response := LicenseResponse{
		ID:       license.ID,
		Name:     license.Name,
		Key:      license.Key,
		Status:   string(license.Status),
		PolicyID: license.PolicyID,
		UserID:   license.UserID,
		Metadata: license.Metadata,
		Created:  license.CreatedAt.Format("2006-01-02T15:04:05Z"),
		Updated:  license.UpdatedAt.Format("2006-01-02T15:04:05Z"),
	}

	if license.ExpiresAt != nil {
		expiresAt := license.ExpiresAt.Format("2006-01-02T15:04:05Z")
		response.ExpiresAt = &expiresAt
	}

	return response
}

// LicenseInfoResponse represents license information response
type LicenseInfoResponse struct {
	Format    string                 `json:"format"`
	Status    string                 `json:"status,omitempty"`
	AccountID *uuid.UUID             `json:"account_id,omitempty"`
	ProductID *uuid.UUID             `json:"product_id,omitempty"`
	ExpiresAt *time.Time             `json:"expires_at,omitempty"`
	Expired   bool                   `json:"expired,omitempty"`
	Suspended bool                   `json:"suspended,omitempty"`
	Claims    map[string]interface{} `json:"claims,omitempty"`
}

// ValidatePostHandler handles POST /api/v1/licenses/validate
func (lh *LicenseHandler) ValidatePostHandler(c *gin.Context) {
	var req ValidateLicenseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responses.RenderBadRequest(c, "Invalid request format: "+err.Error())
		return
	}

	// Perform license validation
	result, err := lh.serviceCoordinator.LicenseValidation.ValidateLicense(
		c.Request.Context(),
		req.LicenseKey,
		req.MachineFingerprint,
		req.Environment,
	)
	if err != nil {
		responses.RenderInternalError(c, "Failed to validate license: "+err.Error())
		return
	}

	// Convert to response format
	response := &ValidateLicenseResponse{
		Valid:           result.Valid,
		ValidationTime:  result.ValidationTime,
		ExpiresAt:       result.ExpiresAt,
		MachinesUsed:    result.MachinesUsed,
		MachinesAllowed: result.MachinesAllowed,
		Claims:          result.Claims,
		Errors:          result.Errors,
		Warnings:        result.Warnings,
	}

	// Include entity details if validation was successful
	if result.Valid {
		if result.License != nil {
			response.License = sanitizeLicense(result.License)
		}
		if result.Policy != nil {
			response.Policy = sanitizePolicy(result.Policy)
		}
		if result.Account != nil {
			response.Account = sanitizeAccount(result.Account)
		}
	}

	// Broadcast validation event (Ruby: BroadcastEventService.call)
	if result.License != nil && result.Account != nil {
		eventType := events.EventLicenseValidationSucceeded
		if !result.Valid {
			eventType = events.EventLicenseValidationFailed
		}

		meta := events.EventMeta{}
		if len(result.Errors) > 0 {
			meta["code"] = result.Errors[0] // First error as code
		}

		// Broadcast the event
		if err := lh.serviceCoordinator.Events.BroadcastEvent(
			c.Request.Context(),
			eventType,
			result.Account,
			events.MakeEventResource(result.License),
			meta,
		); err != nil {
			// Log error but don't fail the validation response
			// Ruby logs this error but continues
			// Log error but don't fail the validation response
		}
	}

	status := http.StatusOK
	if !result.Valid {
		status = http.StatusForbidden
	}

	c.JSON(status, response)
}

// ValidateGetHandler handles GET /api/v1/licenses/validate
func (lh *LicenseHandler) ValidateGetHandler(c *gin.Context) {
	licenseKey := c.Query("license_key")
	if licenseKey == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "missing_license_key",
			"message": "license_key query parameter is required",
		})
		return
	}

	machineFingerprint := c.Query("machine_fingerprint")
	environment := c.Query("environment")

	var machineFingerprintPtr *string
	var environmentPtr *string

	if machineFingerprint != "" {
		machineFingerprintPtr = &machineFingerprint
	}
	if environment != "" {
		environmentPtr = &environment
	}

	// Perform license validation
	result, err := lh.serviceCoordinator.LicenseValidation.ValidateLicense(
		c.Request.Context(),
		licenseKey,
		machineFingerprintPtr,
		environmentPtr,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "validation_error",
			"message": "Failed to validate license",
			"details": err.Error(),
		})
		return
	}

	// Convert to response format
	response := &ValidateLicenseResponse{
		Valid:           result.Valid,
		ValidationTime:  result.ValidationTime,
		ExpiresAt:       result.ExpiresAt,
		MachinesUsed:    result.MachinesUsed,
		MachinesAllowed: result.MachinesAllowed,
		Claims:          result.Claims,
		Errors:          result.Errors,
		Warnings:        result.Warnings,
	}

	// Include entity details if validation was successful
	if result.Valid {
		if result.License != nil {
			response.License = sanitizeLicense(result.License)
		}
		if result.Policy != nil {
			response.Policy = sanitizePolicy(result.Policy)
		}
		if result.Account != nil {
			response.Account = sanitizeAccount(result.Account)
		}
	}

	status := http.StatusOK
	if !result.Valid {
		status = http.StatusForbidden
	}

	c.JSON(status, response)
}

// QuickValidateHandler handles GET /api/v1/licenses/quick-validate
func (lh *LicenseHandler) QuickValidateHandler(c *gin.Context) {
	licenseKey := c.Query("license_key")
	if licenseKey == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "missing_license_key",
			"message": "license_key query parameter is required",
		})
		return
	}

	// Perform quick validation
	valid, err := lh.serviceCoordinator.LicenseValidation.ValidateLicenseQuick(
		c.Request.Context(),
		licenseKey,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "validation_error",
			"message": "Failed to validate license",
			"details": err.Error(),
		})
		return
	}

	response := gin.H{
		"valid":           valid,
		"validation_time": time.Now(),
	}

	status := http.StatusOK
	if !valid {
		status = http.StatusForbidden
	}

	c.JSON(status, response)
}

// InfoHandler handles GET /api/v1/licenses/info
func (lh *LicenseHandler) InfoHandler(c *gin.Context) {
	licenseKey := c.Query("license_key")
	if licenseKey == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "missing_license_key",
			"message": "license_key query parameter is required",
		})
		return
	}

	// Get license information
	info, err := lh.serviceCoordinator.LicenseValidation.GetLicenseInfo(
		c.Request.Context(),
		licenseKey,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "info_error",
			"message": "Failed to get license information",
			"details": err.Error(),
		})
		return
	}

	// Convert to response format
	response := &LicenseInfoResponse{
		Claims: info,
	}

	if format, ok := info["format"].(string); ok {
		response.Format = format
	}
	if status, ok := info["status"].(string); ok {
		response.Status = status
	}
	if accountIDStr, ok := info["account_id"].(string); ok {
		if accountID, err := uuid.Parse(accountIDStr); err == nil {
			response.AccountID = &accountID
		}
	}
	if productIDStr, ok := info["product_id"].(string); ok {
		if productID, err := uuid.Parse(productIDStr); err == nil {
			response.ProductID = &productID
		}
	}
	if expiresAt, ok := info["expires_at"].(*time.Time); ok {
		response.ExpiresAt = expiresAt
	}
	if expired, ok := info["expired"].(bool); ok {
		response.Expired = expired
	}
	if suspended, ok := info["suspended"].(bool); ok {
		response.Suspended = suspended
	}

	c.JSON(http.StatusOK, response)
}

// InvalidateCacheHandler handles POST /api/v1/licenses/invalidate-cache
func (lh *LicenseHandler) InvalidateCacheHandler(c *gin.Context) {
	licenseKey := c.Query("license_key")
	if licenseKey == "" {
		var req struct {
			LicenseKey string `json:"license_key" binding:"required"`
		}
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "invalid_request",
				"message": "license_key is required in query parameter or request body",
			})
			return
		}
		licenseKey = req.LicenseKey
	}

	// Invalidate cache
	err := lh.serviceCoordinator.LicenseValidation.InvalidateCache(
		c.Request.Context(),
		licenseKey,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "cache_error",
			"message": "Failed to invalidate cache",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":     "Cache invalidated successfully",
		"license_key": licenseKey,
	})
}

// ListLicensesHandler handles GET /api/v1/licenses
func (lh *LicenseHandler) ListLicensesHandler(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "authentication_required",
			"message": "Account ID not found in token",
		})
		return
	}

	// Parse query parameters
	page := 1
	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	pageSize := 20
	if pageSizeStr := c.Query("page_size"); pageSizeStr != "" {
		if ps, err := strconv.Atoi(pageSizeStr); err == nil && ps > 0 && ps <= 100 {
			pageSize = ps
		}
	}

	// Build filter
	filter := repositories.ListFilter{
		Page:      page,
		PageSize:  pageSize,
		SortBy:    c.DefaultQuery("sort_by", "created_at"),
		SortOrder: c.DefaultQuery("sort_order", "DESC"),
		Search:    c.Query("search"),
		AccountID: &accountID,
		Filters:   make(map[string]interface{}),
	}

	// Add status filter
	if status := c.Query("status"); status != "" {
		filter.Filters["status = ?"] = status
	}

	// Add product filter
	if productID := c.Query("product_id"); productID != "" {
		if pid, err := uuid.Parse(productID); err == nil {
			filter.Filters["product_id = ?"] = pid
		}
	}

	// Get licenses
	licenses, total, err := lh.serviceCoordinator.Repositories.License().List(c.Request.Context(), filter)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "database_error",
			"message": "Failed to retrieve licenses",
			"details": err.Error(),
		})
		return
	}

	// Sanitize response
	sanitizedLicenses := make([]interface{}, len(licenses))
	for i, license := range licenses {
		sanitizedLicenses[i] = sanitizeLicense(license)
	}

	c.JSON(http.StatusOK, gin.H{
		"licenses": sanitizedLicenses,
		"pagination": gin.H{
			"page":        page,
			"page_size":   pageSize,
			"total":       total,
			"total_pages": (total + int64(pageSize) - 1) / int64(pageSize),
		},
	})
}

// GetLicenseHandler handles GET /api/v1/licenses/:id
func (lh *LicenseHandler) GetLicenseHandler(c *gin.Context) {
	licenseIDStr := c.Param("id")
	licenseID, err := uuid.Parse(licenseIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "invalid_license_id",
			"message": "Invalid license ID format",
		})
		return
	}

	// Get license
	license, err := lh.serviceCoordinator.Repositories.License().GetByID(c.Request.Context(), licenseID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "license_not_found",
			"message": "License not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"license": sanitizeLicense(license),
	})
}

// ValidateByIDHandler handles license validation by ID (Ruby: validate_by_id)
func (lh *LicenseHandler) ValidateByIDHandler(c *gin.Context) {
	licenseID := c.Param("id")
	if licenseID == "" {
		responses.RenderBadRequest(c, "License ID is required")
		return
	}

	// Get account ID from authentication context
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		responses.RenderUnauthorized(c, "Account ID not found in token")
		return
	}

	// Parse license UUID
	licenseUUID, err := uuid.Parse(licenseID)
	if err != nil {
		responses.RenderBadRequest(c, "Invalid license ID format")
		return
	}

	// Get license by ID
	license, err := lh.serviceCoordinator.Repositories.License().GetByID(c.Request.Context(), licenseUUID)
	if err != nil {
		responses.RenderNotFound(c, "License not found")
		return
	}

	// Verify license belongs to the account
	if license.AccountID != accountID.String() {
		responses.RenderNotFound(c, "License not found")
		return
	}

	// Parse query parameters for validation options
	machineFingerprint := c.Query("machine_fingerprint")
	environment := c.Query("environment")
	skipTouch := c.GetHeader("Origin") == "https://app.keygen.sh"

	var machineFingerprintPtr *string
	var environmentPtr *string

	if machineFingerprint != "" {
		machineFingerprintPtr = &machineFingerprint
	}
	if environment != "" {
		environmentPtr = &environment
	}

	// Perform license validation with scope
	result, err := lh.serviceCoordinator.LicenseValidation.ValidateLicense(
		c.Request.Context(),
		license.Key,
		machineFingerprintPtr,
		environmentPtr,
	)
	if err != nil {
		responses.RenderInternalError(c, "Failed to validate license: "+err.Error())
		return
	}

	// Convert to response format
	response := &ValidateLicenseResponse{
		Valid:           result.Valid,
		ValidationTime:  result.ValidationTime,
		ExpiresAt:       result.ExpiresAt,
		MachinesUsed:    result.MachinesUsed,
		MachinesAllowed: result.MachinesAllowed,
		Claims:          result.Claims,
		Errors:          result.Errors,
		Warnings:        result.Warnings,
	}

	// Include entity details if validation was successful
	if result.Valid {
		if result.License != nil {
			response.License = sanitizeLicense(result.License)
		}
		if result.Policy != nil {
			response.Policy = sanitizePolicy(result.Policy)
		}
		if result.Account != nil {
			response.Account = sanitizeAccount(result.Account)
		}
	}

	// Broadcast validation event if not skip_touch
	if !skipTouch && result.License != nil && result.Account != nil {
		eventType := events.EventLicenseValidationSucceeded
		if !result.Valid {
			eventType = events.EventLicenseValidationFailed
		}

		meta := events.EventMeta{}
		if len(result.Errors) > 0 {
			meta["code"] = result.Errors[0]
		}

		lh.serviceCoordinator.Events.BroadcastEvent(
			c.Request.Context(),
			eventType,
			result.Account,
			events.MakeEventResource(result.License),
			meta,
		)
	}

	status := http.StatusOK
	if !result.Valid {
		status = http.StatusForbidden
	}

	c.JSON(status, response)
}

// QuickValidateByIDHandler handles quick license validation by ID (Ruby: quick_validate_by_id)
func (lh *LicenseHandler) QuickValidateByIDHandler(c *gin.Context) {
	licenseID := c.Param("id")
	if licenseID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "License ID is required"})
		return
	}

	// Get account ID from authentication context
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		responses.RenderUnauthorized(c, "Account ID not found in token")
		return
	}

	// Parse license UUID
	licenseUUID, err := uuid.Parse(licenseID)
	if err != nil {
		responses.RenderBadRequest(c, "Invalid license ID format")
		return
	}

	// Get license by ID
	license, err := lh.serviceCoordinator.Repositories.License().GetByID(c.Request.Context(), licenseUUID)
	if err != nil {
		responses.RenderNotFound(c, "License not found")
		return
	}

	// Verify license belongs to the account
	if license.AccountID != accountID.String() {
		responses.RenderNotFound(c, "License not found")
		return
	}

	// Ruby: skip_touch if origin header is https://app.keygen.sh
	// Skip touch if origin header is https://app.keygen.sh (used in validation logic)
	_ = c.GetHeader("Origin") == "https://app.keygen.sh"

	// Quick validation without scope (Ruby: LicenseValidationService.call(license: license, scope: false, skip_touch: skip_touch))
	result, err := lh.serviceCoordinator.LicenseValidation.ValidateLicenseQuick(
		c.Request.Context(),
		license.Key,
	)
	if err != nil {
		responses.RenderInternalError(c, "Failed to validate license: "+err.Error())
		return
	}

	// Ruby response format
	response := gin.H{
		"data": gin.H{
			"id":   licenseID,
			"type": "licenses",
			"attributes": gin.H{
				"key":        license.Key,
				"name":       license.Name,
				"status":     license.Status,
				"suspended":  license.Suspended,
				"expires_at": license.ExpiresAt,
				"created_at": license.CreatedAt,
				"updated_at": license.UpdatedAt,
			},
		},
		"meta": gin.H{
			"ts":     time.Now(),
			"valid":  result,
			"detail": getValidationDetail(result, nil),
			"code":   getValidationCode(result, nil),
		},
	}

	c.JSON(http.StatusOK, response)
}

// ValidateByKeyHandler handles license validation by key (Ruby: validate_by_key)
func (lh *LicenseHandler) ValidateByKeyHandler(c *gin.Context) {
	var req struct {
		Meta *struct {
			Key   string      `json:"key" binding:"required"`
			Nonce *string     `json:"nonce,omitempty"`
			Scope interface{} `json:"scope,omitempty"`
		} `json:"meta"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Perform license validation by key
	result, err := lh.serviceCoordinator.LicenseValidation.ValidateLicense(
		c.Request.Context(),
		req.Meta.Key,
		nil, // No machine fingerprint for this endpoint
		nil, // No environment for this endpoint
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to validate license: " + err.Error()})
		return
	}

	response := gin.H{
		"data": gin.H{
			"key": req.Meta.Key,
		},
		"meta": gin.H{
			"ts":     time.Now(),
			"valid":  result.Valid,
			"detail": getValidationDetail(result.Valid, result.Errors),
			"code":   getValidationCode(result.Valid, result.Errors),
		},
	}

	if req.Meta.Nonce != nil {
		response["meta"].(gin.H)["nonce"] = *req.Meta.Nonce
	}
	if req.Meta.Scope != nil {
		response["meta"].(gin.H)["scope"] = req.Meta.Scope
	}

	c.JSON(http.StatusOK, response)
}

// CheckoutLicenseHandler handles license checkout (Ruby: LicenseCheckoutService)
func (lh *LicenseHandler) CheckoutLicenseHandler(c *gin.Context) {
	licenseID := c.Param("id")
	if licenseID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "License ID is required"})
		return
	}

	// Parse query parameters for checkout options
	var options struct {
		Encrypt   bool     `form:"encrypt"`
		Algorithm string   `form:"algorithm"`
		TTL       *int     `form:"ttl"`
		Include   []string `form:"include"`
	}

	if err := c.ShouldBindQuery(&options); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// For POST requests, also check body
	if c.Request.Method == "POST" {
		var req struct {
			Meta *struct {
				Encrypt   *bool    `json:"encrypt,omitempty"`
				Algorithm *string  `json:"algorithm,omitempty"`
				TTL       *int     `json:"ttl,omitempty"`
				Include   []string `json:"include,omitempty"`
			} `json:"meta,omitempty"`
		}

		if err := c.ShouldBindJSON(&req); err == nil && req.Meta != nil {
			if req.Meta.Encrypt != nil {
				options.Encrypt = *req.Meta.Encrypt
			}
			if req.Meta.Algorithm != nil {
				options.Algorithm = *req.Meta.Algorithm
			}
			if req.Meta.TTL != nil {
				options.TTL = req.Meta.TTL
			}
			if len(req.Meta.Include) > 0 {
				options.Include = req.Meta.Include
			}
		}
	}

	// Implement license checkout using checkout service
	// This should generate a signed license certificate

	// For GET request, return certificate as attachment
	if c.Request.Method == "GET" {
		certificateData := "PLACEHOLDER_CERTIFICATE_DATA" // Actual certificate from checkout service
		c.Header("Content-Disposition", `attachment; filename="`+licenseID+`.lic"`)
		c.Header("Content-Type", "application/octet-stream")
		c.String(http.StatusOK, certificateData)
		return
	}

	// For POST request, return JSON response
	response := gin.H{
		"data": gin.H{
			"id":          licenseID,
			"certificate": "PLACEHOLDER_CERTIFICATE_DATA", // Actual certificate from checkout service
			"algorithm":   options.Algorithm,
			"encrypted":   options.Encrypt,
		},
	}

	c.JSON(http.StatusOK, response)
}

// CreateLicense handles POST /api/v1/licenses - Go-style approach
func (lh *LicenseHandler) CreateLicense(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "unauthorized",
			"message": "Account ID not found in token",
		})
		return
	}

	var req LicenseCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "validation_failed",
			"message": "Invalid request data",
			"details": err.Error(),
		})
		return
	}

	// Generate license key if not provided
	licenseKey := req.Key
	if licenseKey == "" {
		licenseKey = "LIC-" + uuid.New().String()[:8] + "-" + uuid.New().String()[:8]
	}

	// Create license entity
	license := &entities.License{
		AccountID: accountID.String(),
		Name:      req.Name,
		Key:       licenseKey,
		PolicyID:  req.PolicyID,
		UserID:    req.UserID,
		Status:    entities.LicenseStatusActive,
		ExpiresAt: req.ExpiresAt,
		Metadata:  req.Metadata,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Create license
	if err := lh.serviceCoordinator.Repositories.License().Create(c.Request.Context(), license); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "creation_failed",
			"message": "Failed to create license",
			"details": err.Error(),
		})
		return
	}

	// Broadcast event
	account, _ := lh.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	lh.serviceCoordinator.Events.BroadcastEvent(
		c.Request.Context(),
		events.EventLicenseCreated,
		account,
		events.MakeEventResource(license),
		events.EventMeta{},
	)

	// Return created license in Go-style
	response := lh.toLicenseResponse(license)
	c.JSON(http.StatusCreated, response)
}

// CreateLicenseHandler - Legacy handler for backward compatibility
// Use CreateLicense instead
func (lh *LicenseHandler) CreateLicenseHandler(c *gin.Context) {
	lh.CreateLicense(c)
}

// UpdateLicense handles PUT /api/v1/licenses/:id - Go-style approach
func (lh *LicenseHandler) UpdateLicense(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "unauthorized",
			"message": "Account ID not found in token",
		})
		return
	}

	licenseIDStr := c.Param("id")
	licenseID, err := uuid.Parse(licenseIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "invalid_parameter",
			"message": "Invalid license ID format",
		})
		return
	}

	// Get existing license
	license, err := lh.serviceCoordinator.Repositories.License().GetByID(c.Request.Context(), licenseID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "not_found",
			"message": "License not found",
		})
		return
	}

	// Verify license belongs to the account
	if license.AccountID != accountID.String() {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "not_found",
			"message": "License not found",
		})
		return
	}

	var req LicenseUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "validation_failed",
			"message": "Invalid request data",
			"details": err.Error(),
		})
		return
	}

	// Update fields (only non-nil values)
	if req.Name != nil {
		license.Name = *req.Name
	}
	if req.Status != nil {
		license.Status = entities.LicenseStatus(*req.Status)
	}
	if req.ExpiresAt != nil {
		license.ExpiresAt = req.ExpiresAt
	}
	if req.Metadata != nil {
		license.Metadata = req.Metadata
	}
	license.UpdatedAt = time.Now()

	// Update license
	if err := lh.serviceCoordinator.Repositories.License().Update(c.Request.Context(), license); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "update_failed",
			"message": "Failed to update license",
			"details": err.Error(),
		})
		return
	}

	// Broadcast event
	account, _ := lh.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	lh.serviceCoordinator.Events.BroadcastEvent(
		c.Request.Context(),
		events.EventLicenseUpdated,
		account,
		events.MakeEventResource(license),
		events.EventMeta{},
	)

	// Return updated license in Go-style
	response := lh.toLicenseResponse(license)
	c.JSON(http.StatusOK, response)
}

// UpdateLicenseHandler - Legacy handler for backward compatibility
// Use UpdateLicense instead
func (lh *LicenseHandler) UpdateLicenseHandler(c *gin.Context) {
	lh.UpdateLicense(c)
}

// GetLicense handles GET /api/v1/licenses/:id - Go-style approach
func (lh *LicenseHandler) GetLicense(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "unauthorized",
			"message": "Account ID not found in token",
		})
		return
	}

	licenseIDStr := c.Param("id")
	licenseID, err := uuid.Parse(licenseIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "invalid_parameter",
			"message": "Invalid license ID format",
		})
		return
	}

	// Get license
	license, err := lh.serviceCoordinator.Repositories.License().GetByID(c.Request.Context(), licenseID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "not_found",
			"message": "License not found",
		})
		return
	}

	// Verify license belongs to the account
	if license.AccountID != accountID.String() {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "not_found",
			"message": "License not found",
		})
		return
	}

	// Return license
	response := lh.toLicenseResponse(license)
	c.JSON(http.StatusOK, response)
}

// ListLicenses handles GET /api/v1/licenses - Go-style approach
func (lh *LicenseHandler) ListLicenses(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "unauthorized",
			"message": "Account ID not found in token",
		})
		return
	}

	// Parse pagination parameters
	page := 1
	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	perPage := 25
	if perPageStr := c.Query("per_page"); perPageStr != "" {
		if pp, err := strconv.Atoi(perPageStr); err == nil && pp > 0 && pp <= 100 {
			perPage = pp
		}
	}

	// Parse filters
	status := c.Query("status")
	policyID := c.Query("policy_id")
	userID := c.Query("user_id")

	// TODO: Implement repository method with filters and pagination
	// For now, return empty list
	licenses := []entities.License{}
	total := int64(0)

	// Convert to response format
	licenseResponses := make([]LicenseResponse, len(licenses))
	for i, license := range licenses {
		licenseResponses[i] = lh.toLicenseResponse(&license)
	}

	// Calculate pagination
	totalPages := (total + int64(perPage) - 1) / int64(perPage)

	response := LicenseListResponse{
		Licenses: licenseResponses,
		Pagination: PaginationInfo{
			Page:       page,
			PerPage:    perPage,
			Total:      total,
			TotalPages: int(totalPages),
		},
	}

	// Log unused variables for now
	_ = status
	_ = policyID
	_ = userID
	_ = accountID

	c.JSON(http.StatusOK, response)
}

// DeleteLicenseHandler handles DELETE /api/v1/licenses/:id
func (lh *LicenseHandler) DeleteLicenseHandler(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		responses.RenderUnauthorized(c, "Account ID not found in token")
		return
	}

	licenseIDStr := c.Param("id")
	licenseID, err := uuid.Parse(licenseIDStr)
	if err != nil {
		responses.RenderBadRequest(c, "Invalid license ID format")
		return
	}

	// Get existing license
	license, err := lh.serviceCoordinator.Repositories.License().GetByID(c.Request.Context(), licenseID)
	if err != nil {
		responses.RenderNotFound(c, "License not found")
		return
	}

	// Verify license belongs to the account
	if license.AccountID != accountID.String() {
		responses.RenderNotFound(c, "License not found")
		return
	}

	// Delete license
	if err := lh.serviceCoordinator.Repositories.License().Delete(c.Request.Context(), licenseID); err != nil {
		responses.RenderInternalError(c, "Failed to delete license: "+err.Error())
		return
	}

	// Broadcast event
	account, _ := lh.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	lh.serviceCoordinator.Events.BroadcastEvent(
		c.Request.Context(),
		events.EventLicenseDeleted,
		account,
		events.MakeEventResource(license),
		events.EventMeta{},
	)

	c.Status(http.StatusNoContent)
}

// Sanitization functions to remove sensitive data from responses

func sanitizeLicense(license interface{}) interface{} {
	// This would normally convert the license entity to a safe response format
	// removing sensitive fields like internal IDs, secrets, etc.
	return license
}

func sanitizePolicy(policy interface{}) interface{} {
	// This would normally convert the policy entity to a safe response format
	return policy
}

func sanitizeAccount(account interface{}) interface{} {
	// This would normally convert the account entity to a safe response format
	// removing sensitive fields like private keys, secrets, etc.
	return account
}

// Helper functions for validation response mapping

// getValidationDetail returns validation detail string based on result (Ruby: detail)
func getValidationDetail(valid bool, errors []string) string {
	if valid {
		return "valid"
	}
	if len(errors) > 0 {
		// Map common error types to Ruby detail strings
		for _, err := range errors {
			switch err {
			case "LICENSE_NOT_FOUND":
				return "not found"
			case "LICENSE_EXPIRED":
				return "expired"
			case "LICENSE_SUSPENDED":
				return "suspended"
			case "USER_BANNED":
				return "banned"
			case "LICENSE_OVERDUE":
				return "overdue"
			case "TOO_MANY_MACHINES":
				return "too many machines"
			case "FINGERPRINT_SCOPE_MISMATCH":
				return "fingerprint scope mismatch"
			case "ENVIRONMENT_SCOPE_MISMATCH":
				return "environment scope mismatch"
			}
		}
	}
	return "invalid"
}

// getValidationCode returns validation code based on result (Ruby: code)
func getValidationCode(valid bool, errors []string) string {
	if valid {
		return "VALID"
	}
	if len(errors) > 0 {
		// Return the first error as code
		return errors[0]
	}
	return "INVALID"
}
