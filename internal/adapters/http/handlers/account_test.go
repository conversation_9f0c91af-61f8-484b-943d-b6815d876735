package handlers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"github.com/gokeys/gokeys/internal/domain/entities"
)

// TestAccountHandlerBasic tests basic account handler functionality without database
func TestAccountHandlerBasic(t *testing.T) {
	gin.SetMode(gin.TestMode)
	
	t.Run("handler_creation", func(t *testing.T) {
		// Test that we can create the handler without panicking
		handler := NewAccountHandler(nil)
		assert.NotNil(t, handler)
	})

	t.Run("account_update_request_structure", func(t *testing.T) {
		// Test AccountUpdateRequest structure
		requestBody := map[string]interface{}{
			"data": map[string]interface{}{
				"type": "accounts",
				"attributes": map[string]interface{}{
					"name": "Updated Account Name",
					"slug": "updated-account-slug",
					"metadata": map[string]interface{}{
						"billing_address": map[string]interface{}{
							"street": "123 Main St",
							"city":   "San Francisco",
							"state":  "CA",
							"zip":    "94105",
						},
						"subscription_plan": "enterprise",
						"max_licenses":      1000,
					},
				},
			},
		}

		body, err := json.Marshal(requestBody)
		require.NoError(t, err)

		req := httptest.NewRequest("PATCH", "/api/v1/accounts/123", bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")

		assert.Equal(t, "PATCH", req.Method)
		assert.Equal(t, "/api/v1/accounts/123", req.URL.Path)
		assert.Equal(t, "application/json", req.Header.Get("Content-Type"))
	})

	t.Run("json_parsing", func(t *testing.T) {
		// Test that our request structure can be parsed
		jsonStr := `{
			"data": {
				"type": "accounts",
				"attributes": {
					"name": "My Enterprise Account",
					"slug": "my-enterprise",
					"metadata": {
						"company_size": "large",
						"industry": "technology",
						"billing_email": "<EMAIL>",
						"settings": {
							"sso_enabled": true,
							"mfa_required": true,
							"audit_logs": true
						}
					}
				}
			}
		}`

		var requestBody AccountUpdateRequest
		err := json.Unmarshal([]byte(jsonStr), &requestBody)
		require.NoError(t, err)

		assert.Equal(t, "accounts", requestBody.Data.Type)
		assert.Equal(t, "My Enterprise Account", *requestBody.Data.Attributes.Name)
		assert.Equal(t, "my-enterprise", *requestBody.Data.Attributes.Slug)
		assert.Equal(t, "technology", requestBody.Data.Attributes.Metadata["industry"])
		
		// Test nested metadata
		settings := requestBody.Data.Attributes.Metadata["settings"].(map[string]interface{})
		assert.Equal(t, true, settings["sso_enabled"])
	})

	t.Run("slug_validation", func(t *testing.T) {
		// Test slug patterns
		validSlugs := []string{
			"my-account",
			"enterprise-corp",
			"company123",
			"test-org-2024",
		}

		invalidSlugs := []string{
			"My Account",     // spaces
			"account@name",   // special chars
			"UPPERCASE",      // uppercase
			"",               // empty
			"a",              // too short
		}

		for _, slug := range validSlugs {
			assert.Regexp(t, `^[a-z0-9-]+$`, slug, "Valid slug should match pattern")
			assert.True(t, len(slug) >= 2, "Valid slug should be at least 2 characters")
		}

		for _, slug := range invalidSlugs {
			if slug != "" && slug != "a" {
				assert.NotRegexp(t, `^[a-z0-9-]+$`, slug, "Invalid slug should not match pattern")
			}
		}
	})
}

// TestAccountRequestValidation tests account request validation
func TestAccountRequestValidation(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("valid_account_update_request", func(t *testing.T) {
		jsonStr := `{
			"data": {
				"type": "accounts",
				"attributes": {
					"name": "Enterprise Corporation",
					"slug": "enterprise-corp",
					"metadata": {
						"billing_plan": "enterprise",
						"max_users": 500
					}
				}
			}
		}`

		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		
		req := httptest.NewRequest("PATCH", "/", bytes.NewBufferString(jsonStr))
		req.Header.Set("Content-Type", "application/json")
		c.Request = req

		var request AccountUpdateRequest
		err := c.ShouldBindJSON(&request)
		
		require.NoError(t, err)
		assert.Equal(t, "accounts", request.Data.Type)
		assert.Equal(t, "Enterprise Corporation", *request.Data.Attributes.Name)
		assert.Equal(t, "enterprise-corp", *request.Data.Attributes.Slug)
	})

	t.Run("missing_required_fields", func(t *testing.T) {
		testCases := []struct {
			name        string
			jsonStr     string
			shouldError bool
		}{
			{
				name: "missing_type",
				jsonStr: `{
					"data": {
						"attributes": {
							"name": "Test Account"
						}
					}
				}`,
				shouldError: true,
			},
			{
				name: "wrong_type",
				jsonStr: `{
					"data": {
						"type": "users",
						"attributes": {
							"name": "Test Account"
						}
					}
				}`,
				shouldError: false, // Type validation might happen at application level
			},
			{
				name: "empty_attributes",
				jsonStr: `{
					"data": {
						"type": "accounts",
						"attributes": {}
					}
				}`,
				shouldError: false, // Update requests can have partial data
			},
			{
				name: "valid_minimal",
				jsonStr: `{
					"data": {
						"type": "accounts",
						"attributes": {
							"name": "Minimal Account"
						}
					}
				}`,
				shouldError: false,
			},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				
				req := httptest.NewRequest("PATCH", "/", bytes.NewBufferString(tc.jsonStr))
				req.Header.Set("Content-Type", "application/json")
				c.Request = req

				var request AccountUpdateRequest
				err := c.ShouldBindJSON(&request)
				
				if tc.shouldError {
					assert.Error(t, err)
				} else {
					assert.NoError(t, err)
				}
			})
		}
	})
}

// TestAccountHandlerWithRealDB tests the account handler with a real database connection
func TestAccountHandlerWithRealDB(t *testing.T) {
	// Skip if not in integration test mode
	if testing.Short() {
		t.Skip("Skipping database integration test in short mode")
	}

	// Connect to test database
	dsn := "postgres://gokeys:gokeys_dev_password@localhost:5432/gokeys_test?sslmode=disable"
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	require.NoError(t, err, "Failed to connect to test database")

	// Create test tables
	err = db.Exec(`
		CREATE TABLE IF NOT EXISTS test_accounts (
			id VARCHAR(36) PRIMARY KEY,
			name VARCHAR(255) NOT NULL,
			slug VARCHAR(255) UNIQUE NOT NULL,
			email VARCHAR(255) UNIQUE NOT NULL,
			metadata JSONB,
			protected BOOLEAN DEFAULT false,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		);
		
		CREATE TABLE IF NOT EXISTS test_users (
			id VARCHAR(36) PRIMARY KEY,
			account_id VARCHAR(36) NOT NULL,
			first_name VARCHAR(255),
			last_name VARCHAR(255),
			email VARCHAR(255) UNIQUE NOT NULL,
			role VARCHAR(50) DEFAULT 'user',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		);
		
		CREATE TABLE IF NOT EXISTS test_licenses (
			id VARCHAR(36) PRIMARY KEY,
			account_id VARCHAR(36) NOT NULL,
			name VARCHAR(255) NOT NULL,
			key_value VARCHAR(255) NOT NULL,
			status VARCHAR(50) DEFAULT 'active',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		);
	`).Error
	require.NoError(t, err, "Failed to create test tables")

	// Clean up function
	defer func() {
		db.Exec("DROP TABLE IF EXISTS test_licenses CASCADE")
		db.Exec("DROP TABLE IF EXISTS test_users CASCADE")
		db.Exec("DROP TABLE IF EXISTS test_accounts CASCADE")
		sqlDB, _ := db.DB()
		sqlDB.Close()
	}()

	// Insert test data
	testAccount := &entities.Account{
		ID:    uuid.New().String(),
		Name:  "Test Enterprise Account",
		Slug:  "test-enterprise",
		Email: "<EMAIL>",
	}

	// Insert using raw SQL
	metadata := `{"subscription_plan": "enterprise", "max_licenses": 1000, "billing_address": {"city": "San Francisco"}}`
	err = db.Exec(`INSERT INTO test_accounts (id, name, slug, email, metadata) VALUES (?, ?, ?, ?, ?)`,
		testAccount.ID, testAccount.Name, testAccount.Slug, testAccount.Email, metadata).Error
	require.NoError(t, err)

	t.Run("database_tables_created", func(t *testing.T) {
		var count int64
		
		// Check accounts table
		err := db.Raw("SELECT COUNT(*) FROM test_accounts").Scan(&count).Error
		assert.NoError(t, err)
		assert.Equal(t, int64(1), count)
	})

	t.Run("can_query_account_data", func(t *testing.T) {
		var result struct {
			Name     string
			Slug     string
			Email    string
			Metadata string
		}
		
		err := db.Raw("SELECT name, slug, email, metadata FROM test_accounts WHERE id = ?", testAccount.ID).
			Scan(&result).Error
		assert.NoError(t, err)
		assert.Equal(t, "Test Enterprise Account", result.Name)
		assert.Equal(t, "test-enterprise", result.Slug)
		assert.Equal(t, "<EMAIL>", result.Email)
		assert.Contains(t, result.Metadata, "enterprise")
	})

	t.Run("can_update_account_metadata", func(t *testing.T) {
		newMetadata := `{"subscription_plan": "premium", "max_licenses": 2000, "features": ["sso", "audit_logs"]}`
		
		err := db.Exec("UPDATE test_accounts SET metadata = ? WHERE id = ?", newMetadata, testAccount.ID).Error
		assert.NoError(t, err)

		// Verify update
		var updatedMetadata string
		err = db.Raw("SELECT metadata FROM test_accounts WHERE id = ?", testAccount.ID).Scan(&updatedMetadata).Error
		assert.NoError(t, err)
		assert.Contains(t, updatedMetadata, "premium")
		assert.Contains(t, updatedMetadata, "2000")
	})

	t.Run("can_insert_users_for_account", func(t *testing.T) {
		userID := uuid.New().String()
		
		err := db.Exec(`
			INSERT INTO test_users (id, account_id, first_name, last_name, email, role) 
			VALUES (?, ?, ?, ?, ?, ?)`,
			userID, testAccount.ID, "John", "Doe", "<EMAIL>", "admin").Error
		assert.NoError(t, err)

		// Verify user belongs to account
		var userAccountID string
		err = db.Raw("SELECT account_id FROM test_users WHERE id = ?", userID).Scan(&userAccountID).Error
		assert.NoError(t, err)
		assert.Equal(t, testAccount.ID, userAccountID)
	})

	t.Run("can_count_account_resources", func(t *testing.T) {
		// Insert some licenses for the account
		for i := 0; i < 3; i++ {
			licenseID := uuid.New().String()
			err := db.Exec(`
				INSERT INTO test_licenses (id, account_id, name, key_value) 
				VALUES (?, ?, ?, ?)`,
				licenseID, testAccount.ID, fmt.Sprintf("License %d", i+1), fmt.Sprintf("LIC-KEY-%d", i+1)).Error
			require.NoError(t, err)
		}

		// Count licenses for account
		var licenseCount int64
		err := db.Raw("SELECT COUNT(*) FROM test_licenses WHERE account_id = ?", testAccount.ID).Scan(&licenseCount).Error
		assert.NoError(t, err)
		assert.Equal(t, int64(3), licenseCount)

		// Count users for account
		var userCount int64
		err = db.Raw("SELECT COUNT(*) FROM test_users WHERE account_id = ?", testAccount.ID).Scan(&userCount).Error
		assert.NoError(t, err)
		assert.Equal(t, int64(1), userCount) // From previous test
	})

	t.Run("account_slug_uniqueness", func(t *testing.T) {
		// Try to insert account with duplicate slug
		duplicateAccount := uuid.New().String()
		err := db.Exec(`
			INSERT INTO test_accounts (id, name, slug, email) 
			VALUES (?, ?, ?, ?)`,
			duplicateAccount, "Duplicate Account", "test-enterprise", "<EMAIL>").Error
		
		// Should fail due to unique constraint
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "duplicate")
	})

	t.Run("account_request_parsing_with_db", func(t *testing.T) {
		// Test complete account update request
		requestBody := AccountUpdateRequest{}
		requestBody.Data.Type = "accounts"
		requestBody.Data.Attributes.Name = stringPtr("Updated Enterprise Account")
		requestBody.Data.Attributes.Slug = stringPtr("updated-enterprise")
		requestBody.Data.Attributes.Metadata = map[string]interface{}{
			"subscription_plan": "premium",
			"max_licenses":      5000,
			"features": []string{
				"sso",
				"audit_logs",
				"api_access",
				"priority_support",
			},
			"billing_info": map[string]interface{}{
				"plan":         "annual",
				"auto_renew":   true,
				"billing_date": "2025-01-01",
			},
		}

		body, err := json.Marshal(requestBody)
		require.NoError(t, err)

		req := httptest.NewRequest("PATCH", "/api/v1/accounts/"+testAccount.ID, bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer test-token")

		// Create response recorder
		w := httptest.NewRecorder()
		
		// Create basic gin context
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// Test request parsing
		var parsedRequest AccountUpdateRequest
		err = c.ShouldBindJSON(&parsedRequest)
		assert.NoError(t, err)
		assert.Equal(t, "accounts", parsedRequest.Data.Type)
		assert.Equal(t, "Updated Enterprise Account", *parsedRequest.Data.Attributes.Name)
		assert.Equal(t, "updated-enterprise", *parsedRequest.Data.Attributes.Slug)
		assert.Equal(t, "premium", parsedRequest.Data.Attributes.Metadata["subscription_plan"])
		assert.Equal(t, float64(5000), parsedRequest.Data.Attributes.Metadata["max_licenses"])
		
		// Test nested metadata
		billingInfo := parsedRequest.Data.Attributes.Metadata["billing_info"].(map[string]interface{})
		assert.Equal(t, "annual", billingInfo["plan"])
		assert.Equal(t, true, billingInfo["auto_renew"])
	})
}

// TestAccountComplexMetadata tests complex account metadata handling
func TestAccountComplexMetadata(t *testing.T) {
	t.Run("enterprise_metadata_parsing", func(t *testing.T) {
		jsonStr := `{
			"data": {
				"type": "accounts",
				"attributes": {
					"name": "Enterprise Corp",
					"slug": "enterprise-corp",
					"metadata": {
						"subscription": {
							"plan": "enterprise",
							"tier": "premium",
							"billing_cycle": "annual",
							"auto_renew": true,
							"next_billing_date": "2025-12-31T23:59:59Z"
						},
						"limits": {
							"max_licenses": 10000,
							"max_users": 1000,
							"max_api_calls_per_month": 1000000,
							"max_storage_gb": 1000
						},
						"features": {
							"sso": {
								"enabled": true,
								"providers": ["okta", "azure_ad", "google"]
							},
							"audit_logs": {
								"enabled": true,
								"retention_days": 365
							},
							"api_access": {
								"enabled": true,
								"rate_limit": 1000
							},
							"priority_support": true,
							"custom_branding": true
						},
						"billing_info": {
							"company_name": "Enterprise Corp Ltd",
							"tax_id": "TAX123456789",
							"address": {
								"street": "123 Enterprise Blvd",
								"city": "San Francisco",
								"state": "CA",
								"zip": "94105",
								"country": "US"
							},
							"billing_contact": {
								"name": "Jane Smith",
								"email": "<EMAIL>",
								"phone": "******-123-4567"
							}
						},
						"integration_settings": {
							"webhook_url": "https://enterprise-corp.com/webhooks/gokeys",
							"slack_webhook": "https://hooks.slack.com/services/*********/*********/XXXXXXXXXXXXXXXXXXXXXXXX",
							"email_notifications": {
								"license_expiry": true,
								"quota_warnings": true,
								"security_alerts": true
							}
						}
					}
				}
			}
		}`

		var request AccountUpdateRequest
		err := json.Unmarshal([]byte(jsonStr), &request)
		require.NoError(t, err)

		metadata := request.Data.Attributes.Metadata
		assert.NotNil(t, metadata)
		
		// Test subscription metadata
		subscription := metadata["subscription"].(map[string]interface{})
		assert.Equal(t, "enterprise", subscription["plan"])
		assert.Equal(t, true, subscription["auto_renew"])
		
		// Test limits metadata
		limits := metadata["limits"].(map[string]interface{})
		assert.Equal(t, float64(10000), limits["max_licenses"])
		assert.Equal(t, float64(1000000), limits["max_api_calls_per_month"])
		
		// Test features metadata
		features := metadata["features"].(map[string]interface{})
		sso := features["sso"].(map[string]interface{})
		assert.Equal(t, true, sso["enabled"])
		providers := sso["providers"].([]interface{})
		assert.Contains(t, providers, "okta")
		
		// Test billing info
		billingInfo := metadata["billing_info"].(map[string]interface{})
		address := billingInfo["address"].(map[string]interface{})
		assert.Equal(t, "San Francisco", address["city"])
		
		// Test integration settings
		integrationSettings := metadata["integration_settings"].(map[string]interface{})
		emailNotifications := integrationSettings["email_notifications"].(map[string]interface{})
		assert.Equal(t, true, emailNotifications["license_expiry"])
	})
}