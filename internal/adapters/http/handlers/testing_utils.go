package handlers

import (
	"context"
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/gokeys/gokeys/internal/adapters/database/postgres"
	"github.com/gokeys/gokeys/internal/config"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/services"
	"github.com/google/uuid"
	"github.com/rs/zerolog"
	"gorm.io/gorm"
)

var (
	testDB   *gorm.DB
	dbOnce   sync.Once
	dbErr    error
	migrated bool
)

// setupTestDatabaseWithMigrations sets up a test database with all migrations applied
func setupTestDatabaseWithMigrations(t *testing.T) (*gorm.DB, error) {
	t.Helper()

	dbOnce.Do(func() {
		// Load test configuration
		cfg, err := config.Load()
		if err != nil {
			dbErr = err
			return
		}

		// Override database name for tests
		cfg.Database.DBName = "gokeys_test"

		// Create database connection
		dbConfig := postgres.Config{
			Host:            cfg.Database.Host,
			Port:            cfg.Database.Port,
			User:            cfg.Database.User,
			Password:        cfg.Database.Password,
			DBName:          cfg.Database.DBName,
			SSLMode:         cfg.Database.SSLMode,
			MaxIdleConns:    cfg.Database.MaxIdleConns,
			MaxOpenConns:    cfg.Database.MaxOpenConns,
			ConnMaxLifetime: cfg.Database.ConnMaxLifetime,
		}

		testDB, err = postgres.NewConnection(dbConfig)
		if err != nil {
			dbErr = err
			return
		}

		// Run migrations if not already done
		if !migrated {
			// Use GORM AutoMigrate instead of migrate library for tests
			if err := testDB.AutoMigrate(
				&entities.Account{},
				&entities.Environment{},
				&entities.User{},
				&entities.Role{},
				&entities.SecondFactor{},
				&entities.Group{},
				&entities.Product{},
				&entities.Entitlement{},
				&entities.Policy{},
				&entities.License{},
				&entities.Machine{},
				&entities.MachineComponent{},
				&entities.MachineProcess{},
				&entities.Token{},
				&entities.Session{},
				&entities.WebhookEndpoint{},
				&entities.WebhookEvent{},
			); err != nil {
				dbErr = fmt.Errorf("failed to run auto migrations: %w", err)
				return
			}

			migrated = true
			t.Log("✅ Database auto-migrations completed successfully")
		}
	})

	if dbErr != nil {
		t.Skipf("Skipping test due to database setup error: %v", dbErr)
		return nil, dbErr
	}

	return testDB, nil
}

// createTestServiceCoordinator creates a real service coordinator with migrated database
func createTestServiceCoordinator(t *testing.T) (*services.ServiceCoordinator, error) {
	t.Helper()

	db, err := setupTestDatabaseWithMigrations(t)
	if err != nil {
		return nil, err
	}

	// Create real service coordinator with no-op logger for tests
	logger := zerolog.Nop()
	serviceCoordinator := services.NewServiceCoordinator(db, logger)

	return serviceCoordinator, nil
}

// createTestServiceCoordinatorWithDB creates a service coordinator with existing database connection
func createTestServiceCoordinatorWithDB(db *gorm.DB) *services.ServiceCoordinator {
	// Create real service coordinator with no-op logger for tests
	logger := zerolog.Nop()
	serviceCoordinator := services.NewServiceCoordinator(db, logger)

	return serviceCoordinator
}

// cleanupTestData provides helper to cleanup test data after tests
func cleanupTestData(t *testing.T, db *gorm.DB, tables ...string) {
	t.Helper()

	// Clean up tables in reverse dependency order to avoid foreign key issues
	defaultTables := []string{
		"webhook_events",
		"webhook_endpoints",
		"sessions",
		"tokens",
		"machine_processes",
		"machine_components",
		"machines",
		"license_users",
		"license_entitlements",
		"policy_entitlements",
		"group_owners",
		"group_permissions",
		"role_permissions",
		"token_permissions",
		"licenses",
		"entitlements",
		"policies",
		"products",
		"groups",
		"second_factors",
		"roles",
		"users",
		"environments",
	}

	tablesToClean := tables
	if len(tablesToClean) == 0 {
		tablesToClean = defaultTables
	}

	for _, table := range tablesToClean {
		if err := db.Exec("DELETE FROM " + table + " WHERE created_at > NOW() - INTERVAL '1 hour'").Error; err != nil {
			t.Logf("Warning: failed to cleanup table %s: %v", table, err)
		}
	}
}

// withTestTransaction runs a test within a database transaction that gets rolled back
func withTestTransaction(t *testing.T, fn func(*testing.T, *gorm.DB)) {
	t.Helper()

	db, err := setupTestDatabaseWithMigrations(t)
	if err != nil {
		t.Skipf("Skipping test due to database setup: %v", err)
		return
	}

	tx := db.Begin()
	if tx.Error != nil {
		t.Fatalf("Failed to begin transaction: %v", tx.Error)
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r)
		} else {
			tx.Rollback() // Always rollback for tests
		}
	}()

	fn(t, tx)
}

// setupProductionTestDataWithRepositories creates test data using repositories
func setupProductionTestDataWithRepositories(ctx context.Context, sc *services.ServiceCoordinator) (*ProductionTestData, error) {
	// Create test data using repositories (real production workflow)

	// 1. Create Account (required for License)
	account := &entities.Account{
		Name:  "Test Account",
		Slug:  "test-account-" + uuid.New().String()[0:8],
		Email: "<EMAIL>",
		Metadata: entities.Metadata{
			"test_mode":  true,
			"created_by": "integration_test",
		},
	}
	if err := sc.Repositories.Account().Create(ctx, account); err != nil {
		return nil, err
	}

	// 2. Create Environment (optional but useful)
	environment := &entities.Environment{
		AccountID: account.ID,
		Name:      "Test Environment",
		Code:      "test",
	}
	if err := sc.Repositories.Environment().Create(ctx, environment); err != nil {
		return nil, err
	}

	// 3. Create Product (required for License)
	product := &entities.Product{
		AccountID:     account.ID,
		EnvironmentID: &environment.ID,
		Name:          "Test Product",
		Code:          "test-product-" + uuid.New().String()[0:8],
	}
	if err := sc.Repositories.Product().Create(ctx, product); err != nil {
		return nil, err
	}

	// 4. Create Policy (required for License)
	policy := &entities.Policy{
		AccountID:        account.ID,
		ProductID:        product.ID,
		EnvironmentID:    &environment.ID,
		Name:             "Test Policy",
		Duration:         utilsIntPtr(3600), // 1 hour
		Strict:           false,
		RequireHeartbeat: false,
		MaxMachines:      utilsIntPtr(10),
		MaxUsers:         utilsIntPtr(5),
		Scheme:           entities.Ed25519Sign,
	}
	if err := sc.Repositories.Policy().Create(ctx, policy); err != nil {
		return nil, err
	}

	// 5. Create License (requires Account, Product, Policy)
	license := &entities.License{
		AccountID:     account.ID,
		ProductID:     product.ID,
		PolicyID:      policy.ID,
		EnvironmentID: &environment.ID,
		Key:           "TEST-LIC-" + uuid.New().String()[0:8] + "-" + uuid.New().String()[0:8],
		Name:          "Test License",
		Status:        entities.LicenseStatusActive,
		ExpiresAt:     utilsTimePtr(time.Now().Add(24 * time.Hour)), // Expires in 24 hours
		Metadata: entities.Metadata{
			"test_mode": true,
		},
	}
	if err := sc.Repositories.License().Create(ctx, license); err != nil {
		return nil, err
	}

	return &ProductionTestData{
		Account:     account,
		Product:     product,
		Policy:      policy,
		License:     license,
		Environment: environment,
	}, nil
}

// Helper functions for pointer creation (utils version)
func utilsIntPtr(i int) *int {
	return &i
}

func utilsTimePtr(t time.Time) *time.Time {
	return &t
}

// ProductionTestData holds test data entities for testing
type ProductionTestData struct {
	Account     *entities.Account
	Product     *entities.Product
	Policy      *entities.Policy
	License     *entities.License
	Environment *entities.Environment
}
