package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/adapters/http/middleware"
	"github.com/gokeys/gokeys/internal/adapters/http/responses"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/gokeys/gokeys/internal/domain/services"
	"github.com/gokeys/gokeys/internal/domain/services/events"
	"github.com/google/uuid"
)

type MachineHandler struct {
	serviceCoordinator *services.ServiceCoordinator
}

func NewMachineHandler(serviceCoordinator *services.ServiceCoordinator) *MachineHandler {
	return &MachineHandler{
		serviceCoordinator: serviceCoordinator,
	}
}

// Go-style request structs - simple and clear
type MachineCreateRequest struct {
	Name        string                 `json:"name,omitempty"`
	Fingerprint string                 `json:"fingerprint" binding:"required,min=1,max=255"`
	Platform    string                 `json:"platform,omitempty"`
	Hostname    string                 `json:"hostname,omitempty"`
	IP          string                 `json:"ip,omitempty" binding:"omitempty,ip"`
	Cores       *int                   `json:"cores,omitempty" binding:"omitempty,min=1"`
	LicenseID   string                 `json:"license_id" binding:"required,uuid"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

type MachineUpdateRequest struct {
	Name        *string                `json:"name,omitempty"`
	Fingerprint *string                `json:"fingerprint,omitempty" binding:"omitempty,min=1,max=255"`
	Platform    *string                `json:"platform,omitempty"`
	Hostname    *string                `json:"hostname,omitempty"`
	IP          *string                `json:"ip,omitempty" binding:"omitempty,ip"`
	Cores       *int                   `json:"cores,omitempty" binding:"omitempty,min=1"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// Go-style response structs
type MachineResponse struct {
	ID            string                 `json:"id"`
	Name          string                 `json:"name,omitempty"`
	Fingerprint   string                 `json:"fingerprint"`
	Platform      string                 `json:"platform,omitempty"`
	Hostname      string                 `json:"hostname,omitempty"`
	IP            string                 `json:"ip,omitempty"`
	Cores         *int                   `json:"cores,omitempty"`
	LicenseID     string                 `json:"license_id"`
	PolicyID      string                 `json:"policy_id"`
	GroupID       *string                `json:"group_id,omitempty"`
	OwnerID       *string                `json:"owner_id,omitempty"`
	EnvironmentID *string                `json:"environment_id,omitempty"`
	Status        string                 `json:"status"`
	LastSeen      *string                `json:"last_seen,omitempty"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
	Created       string                 `json:"created_at"`
	Updated       string                 `json:"updated_at"`
}

type MachineListResponse struct {
	Machines   []MachineResponse `json:"machines"`
	Pagination PaginationInfo    `json:"pagination"`
}

// Helper function to convert entity to response
func (h *MachineHandler) toMachineResponse(machine *entities.Machine) MachineResponse {
	response := MachineResponse{
		ID:            machine.ID,
		Name:          machine.Name,
		Fingerprint:   machine.Fingerprint,
		Platform:      machine.Platform,
		Hostname:      machine.Hostname,
		IP:            machine.IP,
		Cores:         &machine.Cores,
		LicenseID:     machine.LicenseID,
		PolicyID:      machine.PolicyID,
		GroupID:       machine.GroupID,
		OwnerID:       machine.OwnerID,
		EnvironmentID: machine.EnvironmentID,
		Status:        string(machine.Status),
		Metadata:      machine.Metadata,
		Created:       machine.CreatedAt.Format("2006-01-02T15:04:05Z"),
		Updated:       machine.UpdatedAt.Format("2006-01-02T15:04:05Z"),
	}

	// Handle optional time fields
	if machine.LastSeen != nil {
		lastSeen := machine.LastSeen.Format("2006-01-02T15:04:05Z")
		response.LastSeen = &lastSeen
	}

	// Handle cores field (convert int to *int for response)
	if machine.Cores > 0 {
		response.Cores = &machine.Cores
	} else {
		response.Cores = nil
	}

	return response
}

// ListMachines handles GET /api/v1/machines - Go-style approach
func (h *MachineHandler) ListMachines(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "unauthorized",
			"message": "Account ID not found in token",
		})
		return
	}

	// Parse pagination parameters
	page := 1
	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	perPage := 25
	if perPageStr := c.Query("per_page"); perPageStr != "" {
		if pp, err := strconv.Atoi(perPageStr); err == nil && pp > 0 && pp <= 100 {
			perPage = pp
		}
	}

	// Parse filters
	search := c.Query("search")
	licenseID := c.Query("license_id")
	policyID := c.Query("policy_id")
	groupID := c.Query("group_id")
	ownerID := c.Query("owner_id")
	status := c.Query("status")
	platform := c.Query("platform")

	// Build filter
	filter := repositories.ListFilter{
		Page:      page,
		PageSize:  perPage,
		SortBy:    c.DefaultQuery("sort_by", "created_at"),
		SortOrder: c.DefaultQuery("sort_order", "DESC"),
		Search:    search,
		AccountID: &accountID,
		Filters:   make(map[string]interface{}),
	}

	// Add specific filters
	if licenseID != "" {
		if lid, err := uuid.Parse(licenseID); err == nil {
			filter.Filters["license_id"] = lid
		}
	}
	if policyID != "" {
		if pid, err := uuid.Parse(policyID); err == nil {
			filter.Filters["policy_id"] = pid
		}
	}
	if groupID != "" {
		if gid, err := uuid.Parse(groupID); err == nil {
			filter.Filters["group_id"] = gid
		}
	}
	if ownerID != "" {
		if oid, err := uuid.Parse(ownerID); err == nil {
			filter.Filters["owner_id"] = oid
		}
	}
	if status != "" {
		filter.Filters["status"] = status
	}
	if platform != "" {
		filter.Filters["platform"] = platform
	}

	// Get machines from repository
	machines, total, err := h.serviceCoordinator.Repositories.Machine().List(c.Request.Context(), filter)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "retrieval_failed",
			"message": "Failed to retrieve machines",
			"details": err.Error(),
		})
		return
	}

	// Convert to response format
	machineResponses := make([]MachineResponse, len(machines))
	for i, machine := range machines {
		machineResponses[i] = h.toMachineResponse(machine)
	}

	// Calculate pagination
	totalPages := (total + int64(perPage) - 1) / int64(perPage)

	response := MachineListResponse{
		Machines: machineResponses,
		Pagination: PaginationInfo{
			Page:       page,
			PerPage:    perPage,
			Total:      total,
			TotalPages: int(totalPages),
		},
	}

	c.JSON(http.StatusOK, response)
}

// ListMachinesHandler - Legacy handler for backward compatibility
// Use ListMachines instead
func (h *MachineHandler) ListMachinesHandler(c *gin.Context) {
	h.ListMachines(c)
}

// GetMachine handles GET /api/v1/machines/:id - Go-style approach
func (h *MachineHandler) GetMachine(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "unauthorized",
			"message": "Account ID not found in token",
		})
		return
	}

	machineIDStr := c.Param("id")
	if machineIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "invalid_parameter",
			"message": "Machine ID is required",
		})
		return
	}

	// Parse machine UUID (supports both ID and fingerprint lookup)
	machineID, err := uuid.Parse(machineIDStr)
	if err != nil {
		// Try to find by fingerprint if not a valid UUID
		machines, _, err := h.serviceCoordinator.Repositories.Machine().List(c.Request.Context(), repositories.ListFilter{
			AccountID: &accountID,
			Filters:   map[string]interface{}{"fingerprint": machineIDStr},
			PageSize:  1,
		})
		if err != nil || len(machines) == 0 {
			c.JSON(http.StatusNotFound, gin.H{
				"error":   "not_found",
				"message": "Machine not found",
			})
			return
		}

		// Return machine found by fingerprint in Go-style
		response := h.toMachineResponse(machines[0])
		c.JSON(http.StatusOK, response)
		return
	}

	// Get machine by ID
	machine, err := h.serviceCoordinator.Repositories.Machine().GetByID(c.Request.Context(), machineID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "not_found",
			"message": "Machine not found",
		})
		return
	}

	// Verify machine belongs to the account
	if machine.AccountID != accountID.String() {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "not_found",
			"message": "Machine not found",
		})
		return
	}

	// Return machine in Go-style
	response := h.toMachineResponse(machine)
	c.JSON(http.StatusOK, response)
}

// GetMachineHandler - Legacy handler for backward compatibility
// Use GetMachine instead
func (h *MachineHandler) GetMachineHandler(c *gin.Context) {
	h.GetMachine(c)
}

// CreateMachine handles POST /api/v1/machines - Go-style approach
func (h *MachineHandler) CreateMachine(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "unauthorized",
			"message": "Account ID not found in token",
		})
		return
	}

	var req MachineCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "validation_failed",
			"message": "Invalid request data",
			"details": err.Error(),
		})
		return
	}

	// Get license to validate and extract policy info
	licenseID, err := uuid.Parse(req.LicenseID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "validation_failed",
			"message": "Invalid license ID format",
		})
		return
	}

	license, err := h.serviceCoordinator.Repositories.License().GetByID(c.Request.Context(), licenseID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "validation_failed",
			"message": "License not found",
		})
		return
	}

	// Verify license belongs to the account
	if license.AccountID != accountID.String() {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "validation_failed",
			"message": "License not found",
		})
		return
	}

	// Create machine entity
	machine := &entities.Machine{
		AccountID:     accountID.String(),
		LicenseID:     req.LicenseID,
		PolicyID:      license.PolicyID,
		EnvironmentID: license.EnvironmentID,
		Name:          req.Name,
		Fingerprint:   req.Fingerprint,
		Platform:      req.Platform,
		Hostname:      req.Hostname,
		IP:            req.IP,
		Status:        entities.MachineStatusActive,
		Metadata:      req.Metadata,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	// Set optional fields
	if req.Cores != nil {
		machine.Cores = *req.Cores
	}

	// Save machine to repository
	err = h.serviceCoordinator.Repositories.Machine().Create(c.Request.Context(), machine)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "creation_failed",
			"message": "Failed to create machine",
			"details": err.Error(),
		})
		return
	}

	// Broadcast machine created event
	account, err := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	if err == nil {
		h.serviceCoordinator.Events.BroadcastEvent(
			c.Request.Context(),
			events.EventMachineCreated,
			account,
			events.MakeEventResource(machine),
			events.EventMeta{},
		)
	}

	// Return created machine in Go-style
	response := h.toMachineResponse(machine)
	c.Header("Location", "/api/v1/machines/"+machine.ID)
	c.JSON(http.StatusCreated, response)
}

// CreateMachineHandler - Legacy handler for backward compatibility
// Use CreateMachine instead
func (h *MachineHandler) CreateMachineHandler(c *gin.Context) {
	h.CreateMachine(c)
}

// HeartbeatPingHandler handles POST /api/v1/machines/:id/actions/heartbeats/ping (Ruby: HeartbeatsController#ping)
func (h *MachineHandler) HeartbeatPingHandler(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		responses.RenderUnauthorized(c, "Account ID not found in token")
		return
	}

	machineID := c.Param("id")
	if machineID == "" {
		responses.RenderBadRequest(c, "Machine ID is required")
		return
	}

	// Parse machine UUID
	machineUUID, err := uuid.Parse(machineID)
	if err != nil {
		responses.RenderBadRequest(c, "Invalid machine ID format")
		return
	}

	// Get machine
	machine, err := h.serviceCoordinator.Repositories.Machine().GetByID(c.Request.Context(), machineUUID)
	if err != nil {
		responses.RenderNotFound(c, "Machine not found")
		return
	}

	// Verify machine belongs to the account
	if machine.AccountID != accountID.String() {
		responses.RenderNotFound(c, "Machine not found")
		return
	}

	// Ruby heartbeat logic:
	// - If machine is dead -> resurrect (machine.heartbeat.resurrected)
	// - Else -> ping (machine.heartbeat.ping)
	now := time.Now()
	wasAlive := machine.LastHeartbeatAt != nil

	// Update machine heartbeat
	machine.LastHeartbeatAt = &now
	machine.UpdatedAt = now

	err = h.serviceCoordinator.Repositories.Machine().Update(c.Request.Context(), machine)
	if err != nil {
		responses.RenderInternalError(c, "Failed to update machine heartbeat: "+err.Error())
		return
	}

	// Broadcast heartbeat event (Ruby: BroadcastEventService.call)
	// Ruby logic: if machine was dead -> resurrect event, else -> ping event
	eventType := events.EventMachineHeartbeatPing
	if !wasAlive {
		eventType = events.EventMachineHeartbeatResurrected
	}

	// Get account for event broadcasting
	account, err := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	if err == nil {
		if err := h.serviceCoordinator.Events.BroadcastEvent(
			c.Request.Context(),
			eventType,
			account,
			events.MakeEventResource(machine),
			events.EventMeta{},
		); err != nil {
			// Log error but don't fail the heartbeat response
			// Log error but don't fail the heartbeat response
		}
	}

	response := gin.H{
		"data": machine,
		"meta": gin.H{
			"ts":        now,
			"ping_time": now,
		},
	}

	c.JSON(http.StatusOK, response)
}

// HeartbeatResetHandler handles POST /api/v1/machines/:id/actions/heartbeats/reset (Ruby: HeartbeatsController#reset)
func (h *MachineHandler) HeartbeatResetHandler(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		responses.RenderUnauthorized(c, "Account ID not found in token")
		return
	}

	machineID := c.Param("id")
	if machineID == "" {
		responses.RenderBadRequest(c, "Machine ID is required")
		return
	}

	// Parse machine UUID
	machineUUID, err := uuid.Parse(machineID)
	if err != nil {
		responses.RenderBadRequest(c, "Invalid machine ID format")
		return
	}

	// Get machine
	machine, err := h.serviceCoordinator.Repositories.Machine().GetByID(c.Request.Context(), machineUUID)
	if err != nil {
		responses.RenderNotFound(c, "Machine not found")
		return
	}

	// Verify machine belongs to the account
	if machine.AccountID != accountID.String() {
		responses.RenderNotFound(c, "Machine not found")
		return
	}

	// Reset machine heartbeat (Ruby: sets last_heartbeat_at to nil)
	now := time.Now()
	machine.LastHeartbeatAt = nil
	machine.UpdatedAt = now

	err = h.serviceCoordinator.Repositories.Machine().Update(c.Request.Context(), machine)
	if err != nil {
		responses.RenderInternalError(c, "Failed to reset machine heartbeat: "+err.Error())
		return
	}

	// Broadcast heartbeat reset event
	account, err := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	if err == nil {
		h.serviceCoordinator.Events.BroadcastEvent(
			c.Request.Context(),
			events.EventMachineHeartbeatReset,
			account,
			events.MakeEventResource(machine),
			events.EventMeta{},
		)
	}

	response := gin.H{
		"data": machine,
		"meta": gin.H{
			"ts":         now,
			"reset_time": now,
		},
	}

	c.JSON(http.StatusOK, response)
}

// CheckoutMachineHandler handles machine checkout (Ruby: MachineCheckoutService)
func (mh *MachineHandler) CheckoutMachineHandler(c *gin.Context) {
	machineID := c.Param("id")
	if machineID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Machine ID is required"})
		return
	}

	// Parse query parameters for checkout options
	var options struct {
		Encrypt   bool     `form:"encrypt"`
		Algorithm string   `form:"algorithm"`
		TTL       *int     `form:"ttl"`
		Include   []string `form:"include"`
	}

	if err := c.ShouldBindQuery(&options); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// For POST requests, also check body
	if c.Request.Method == "POST" {
		var req struct {
			Meta *struct {
				Encrypt   *bool    `json:"encrypt,omitempty"`
				Algorithm *string  `json:"algorithm,omitempty"`
				TTL       *int     `json:"ttl,omitempty"`
				Include   []string `json:"include,omitempty"`
			} `json:"meta,omitempty"`
		}

		if err := c.ShouldBindJSON(&req); err == nil && req.Meta != nil {
			if req.Meta.Encrypt != nil {
				options.Encrypt = *req.Meta.Encrypt
			}
			if req.Meta.Algorithm != nil {
				options.Algorithm = *req.Meta.Algorithm
			}
			if req.Meta.TTL != nil {
				options.TTL = req.Meta.TTL
			}
			if len(req.Meta.Include) > 0 {
				options.Include = req.Meta.Include
			}
		}
	}

	// Implement machine checkout using checkout service
	// This should generate a signed machine certificate

	// For GET request, return certificate as attachment
	if c.Request.Method == "GET" {
		certificateData := "PLACEHOLDER_MACHINE_CERTIFICATE_DATA" // Actual certificate from checkout service
		c.Header("Content-Disposition", `attachment; filename="`+machineID+`.lic"`)
		c.Header("Content-Type", "application/octet-stream")
		c.String(http.StatusOK, certificateData)
		return
	}

	// For POST request, return JSON response
	response := gin.H{
		"data": gin.H{
			"id":          machineID,
			"certificate": "PLACEHOLDER_MACHINE_CERTIFICATE_DATA", // Actual certificate from checkout service
			"algorithm":   options.Algorithm,
			"encrypted":   options.Encrypt,
		},
	}

	c.JSON(http.StatusOK, response)
}

// UpdateMachine handles PUT /api/v1/machines/:id - Go-style approach
func (h *MachineHandler) UpdateMachine(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "unauthorized",
			"message": "Account ID not found in token",
		})
		return
	}

	machineIDStr := c.Param("id")
	machineID, err := uuid.Parse(machineIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "invalid_parameter",
			"message": "Invalid machine ID format",
		})
		return
	}

	var req MachineUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "validation_failed",
			"message": "Invalid request data",
			"details": err.Error(),
		})
		return
	}

	// Get existing machine
	machine, err := h.serviceCoordinator.Repositories.Machine().GetByID(c.Request.Context(), machineID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "not_found",
			"message": "Machine not found",
		})
		return
	}

	// Verify machine belongs to the account
	if machine.AccountID != accountID.String() {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "not_found",
			"message": "Machine not found",
		})
		return
	}

	// Update fields (only non-nil values)
	if req.Name != nil {
		machine.Name = *req.Name
	}
	if req.Fingerprint != nil {
		machine.Fingerprint = *req.Fingerprint
	}
	if req.Platform != nil {
		machine.Platform = *req.Platform
	}
	if req.Hostname != nil {
		machine.Hostname = *req.Hostname
	}
	if req.IP != nil {
		machine.IP = *req.IP
	}
	if req.Cores != nil {
		machine.Cores = *req.Cores
	}
	if req.Metadata != nil {
		machine.Metadata = req.Metadata
	}
	machine.UpdatedAt = time.Now()

	// Update machine
	if err := h.serviceCoordinator.Repositories.Machine().Update(c.Request.Context(), machine); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "update_failed",
			"message": "Failed to update machine",
			"details": err.Error(),
		})
		return
	}

	// Broadcast event
	account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	h.serviceCoordinator.Events.BroadcastEvent(
		c.Request.Context(),
		events.EventMachineUpdated,
		account,
		events.MakeEventResource(machine),
		events.EventMeta{},
	)

	// Return updated machine in Go-style
	response := h.toMachineResponse(machine)
	c.JSON(http.StatusOK, response)
}

// UpdateMachineHandler - Legacy handler for backward compatibility
// Use UpdateMachine instead
func (h *MachineHandler) UpdateMachineHandler(c *gin.Context) {
	h.UpdateMachine(c)
}
	})
}

// DeleteMachineHandler handles DELETE /api/v1/machines/:id
func (mh *MachineHandler) DeleteMachineHandler(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		responses.RenderUnauthorized(c, "Account ID not found in token")
		return
	}

	machineIDStr := c.Param("id")
	machineID, err := uuid.Parse(machineIDStr)
	if err != nil {
		responses.RenderBadRequest(c, "Invalid machine ID format")
		return
	}

	// Get existing machine
	machine, err := mh.serviceCoordinator.Repositories.Machine().GetByID(c.Request.Context(), machineID)
	if err != nil {
		responses.RenderNotFound(c, "Machine not found")
		return
	}

	// Verify machine belongs to the account
	if machine.AccountID != accountID.String() {
		responses.RenderNotFound(c, "Machine not found")
		return
	}

	// Delete machine
	if err := mh.serviceCoordinator.Repositories.Machine().Delete(c.Request.Context(), machineID); err != nil {
		responses.RenderInternalError(c, "Failed to delete machine: "+err.Error())
		return
	}

	// Broadcast event
	account, _ := mh.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	mh.serviceCoordinator.Events.BroadcastEvent(
		c.Request.Context(),
		events.EventMachineDeleted,
		account,
		events.MakeEventResource(machine),
		events.EventMeta{},
	)

	c.Status(http.StatusNoContent)
}
