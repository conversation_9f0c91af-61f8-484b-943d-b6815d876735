package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/adapters/http/middleware"
	"github.com/gokeys/gokeys/internal/adapters/http/responses"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/gokeys/gokeys/internal/domain/services"
	"github.com/gokeys/gokeys/internal/domain/services/events"
	"github.com/google/uuid"
)

type MachineHandler struct {
	serviceCoordinator *services.ServiceCoordinator
}

func NewMachineHandler(serviceCoordinator *services.ServiceCoordinator) *MachineHandler {
	return &MachineHandler{
		serviceCoordinator: serviceCoordinator,
	}
}

type MachineRequest struct {
	Data struct {
		Type       string `json:"type" binding:"required"`
		Attributes struct {
			Name        string                 `json:"name,omitempty"`
			Fingerprint string                 `json:"fingerprint" binding:"required"`
			Platform    *string                `json:"platform,omitempty"`
			Hostname    *string                `json:"hostname,omitempty"`
			IP          *string                `json:"ip,omitempty"`
			Cores       *int                   `json:"cores,omitempty"`
			Metadata    map[string]interface{} `json:"metadata,omitempty"`
		} `json:"attributes"`
		Relationships *struct {
			License *struct {
				Data *struct {
					Type string `json:"type"`
					ID   string `json:"id"`
				} `json:"data"`
			} `json:"license,omitempty"`
		} `json:"relationships,omitempty"`
	} `json:"data"`
}

// ListMachinesHandler handles GET /api/v1/machines (Ruby: MachinesController#index)
func (h *MachineHandler) ListMachinesHandler(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		responses.RenderUnauthorized(c, "Account ID not found in token")
		return
	}

	// Parse pagination
	page := 1
	limit := 25
	if p := c.Query("page"); p != "" {
		if parsed, err := strconv.Atoi(p); err == nil && parsed > 0 {
			page = parsed
		}
	}
	if l := c.Query("limit"); l != "" {
		if parsed, err := strconv.Atoi(l); err == nil && parsed > 0 && parsed <= 100 {
			limit = parsed
		}
	}

	// Build filter (Ruby: has_scope filters)
	filter := repositories.ListFilter{
		Page:      page,
		PageSize:  limit,
		SortBy:    c.DefaultQuery("sort_by", "created_at"),
		SortOrder: c.DefaultQuery("sort_order", "DESC"),
		Search:    c.Query("search"),
		AccountID: &accountID,
		Filters:   make(map[string]interface{}),
	}

	// Add license filter if provided
	if licenseID := c.Query("license_id"); licenseID != "" {
		filter.Filters["license_id = ?"] = licenseID
	}

	// Get machines from repository
	machines, total, err := h.serviceCoordinator.Repositories.Machine().List(c.Request.Context(), filter)
	if err != nil {
		responses.RenderInternalError(c, "Failed to retrieve machines: "+err.Error())
		return
	}

	response := gin.H{
		"data": machines,
		"meta": gin.H{
			"page":        page,
			"limit":       limit,
			"total":       total,
			"total_pages": (total + int64(limit) - 1) / int64(limit),
		},
	}

	c.JSON(http.StatusOK, response)
}

// GetMachineHandler handles GET /api/v1/machines/:id (Ruby: MachinesController#show)
func (h *MachineHandler) GetMachineHandler(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		responses.RenderUnauthorized(c, "Account ID not found in token")
		return
	}

	machineID := c.Param("id")
	if machineID == "" {
		responses.RenderBadRequest(c, "Machine ID is required")
		return
	}

	// Parse machine UUID (Ruby: FindByAliasService allows ID or fingerprint)
	machineUUID, err := uuid.Parse(machineID)
	if err != nil {
		// Try to find by fingerprint if not a valid UUID
		machines, _, err := h.serviceCoordinator.Repositories.Machine().List(c.Request.Context(), repositories.ListFilter{
			AccountID: &accountID,
			Filters:   map[string]interface{}{"fingerprint = ?": machineID},
			PageSize:  1,
		})
		if err != nil || len(machines) == 0 {
			responses.RenderNotFound(c, "Machine not found")
			return
		}
		c.JSON(http.StatusOK, gin.H{"data": machines[0]})
		return
	}

	// Get machine by ID
	machine, err := h.serviceCoordinator.Repositories.Machine().GetByID(c.Request.Context(), machineUUID)
	if err != nil {
		responses.RenderNotFound(c, "Machine not found")
		return
	}

	// Verify machine belongs to the account
	if machine.AccountID != accountID.String() {
		responses.RenderNotFound(c, "Machine not found")
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": machine})
}

// CreateMachineHandler handles POST /api/v1/machines (Ruby: MachinesController#create)
func (h *MachineHandler) CreateMachineHandler(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		responses.RenderUnauthorized(c, "Account ID not found in token")
		return
	}

	var req MachineRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responses.RenderBadRequest(c, "Invalid request format: "+err.Error())
		return
	}

	// Create machine entity
	machine := &entities.Machine{
		ID:          uuid.New().String(),
		AccountID:   accountID.String(),
		Name:        req.Data.Attributes.Name,
		Fingerprint: req.Data.Attributes.Fingerprint,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// Set optional fields
	if req.Data.Attributes.Platform != nil {
		machine.Platform = *req.Data.Attributes.Platform
	}
	if req.Data.Attributes.Hostname != nil {
		machine.Hostname = *req.Data.Attributes.Hostname
	}
	if req.Data.Attributes.IP != nil {
		machine.IP = *req.Data.Attributes.IP
	}
	if req.Data.Attributes.Cores != nil {
		machine.Cores = *req.Data.Attributes.Cores
	}
	if req.Data.Attributes.Metadata != nil {
		machine.Metadata = entities.Metadata(req.Data.Attributes.Metadata)
	}

	// Set license relationship if provided
	if req.Data.Relationships != nil && req.Data.Relationships.License != nil && req.Data.Relationships.License.Data != nil {
		machine.LicenseID = req.Data.Relationships.License.Data.ID
	}

	// Save machine to repository
	err = h.serviceCoordinator.Repositories.Machine().Create(c.Request.Context(), machine)
	if err != nil {
		responses.RenderInternalError(c, "Failed to create machine: "+err.Error())
		return
	}

	// Broadcast machine created event
	account, err := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	if err == nil {
		h.serviceCoordinator.Events.BroadcastEvent(
			c.Request.Context(),
			events.EventMachineCreated,
			account,
			events.MakeEventResource(machine),
			events.EventMeta{},
		)
	}

	c.Header("Location", "/api/v1/machines/"+machine.ID)
	c.JSON(http.StatusCreated, gin.H{"data": machine})
}

// HeartbeatPingHandler handles POST /api/v1/machines/:id/actions/heartbeats/ping (Ruby: HeartbeatsController#ping)
func (h *MachineHandler) HeartbeatPingHandler(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		responses.RenderUnauthorized(c, "Account ID not found in token")
		return
	}

	machineID := c.Param("id")
	if machineID == "" {
		responses.RenderBadRequest(c, "Machine ID is required")
		return
	}

	// Parse machine UUID
	machineUUID, err := uuid.Parse(machineID)
	if err != nil {
		responses.RenderBadRequest(c, "Invalid machine ID format")
		return
	}

	// Get machine
	machine, err := h.serviceCoordinator.Repositories.Machine().GetByID(c.Request.Context(), machineUUID)
	if err != nil {
		responses.RenderNotFound(c, "Machine not found")
		return
	}

	// Verify machine belongs to the account
	if machine.AccountID != accountID.String() {
		responses.RenderNotFound(c, "Machine not found")
		return
	}

	// Ruby heartbeat logic:
	// - If machine is dead -> resurrect (machine.heartbeat.resurrected)
	// - Else -> ping (machine.heartbeat.ping)
	now := time.Now()
	wasAlive := machine.LastHeartbeatAt != nil

	// Update machine heartbeat
	machine.LastHeartbeatAt = &now
	machine.UpdatedAt = now

	err = h.serviceCoordinator.Repositories.Machine().Update(c.Request.Context(), machine)
	if err != nil {
		responses.RenderInternalError(c, "Failed to update machine heartbeat: "+err.Error())
		return
	}

	// Broadcast heartbeat event (Ruby: BroadcastEventService.call)
	// Ruby logic: if machine was dead -> resurrect event, else -> ping event
	eventType := events.EventMachineHeartbeatPing
	if !wasAlive {
		eventType = events.EventMachineHeartbeatResurrected
	}

	// Get account for event broadcasting
	account, err := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	if err == nil {
		if err := h.serviceCoordinator.Events.BroadcastEvent(
			c.Request.Context(),
			eventType,
			account,
			events.MakeEventResource(machine),
			events.EventMeta{},
		); err != nil {
			// Log error but don't fail the heartbeat response
			// Log error but don't fail the heartbeat response
		}
	}

	response := gin.H{
		"data": machine,
		"meta": gin.H{
			"ts":        now,
			"ping_time": now,
		},
	}

	c.JSON(http.StatusOK, response)
}

// HeartbeatResetHandler handles POST /api/v1/machines/:id/actions/heartbeats/reset (Ruby: HeartbeatsController#reset)
func (h *MachineHandler) HeartbeatResetHandler(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		responses.RenderUnauthorized(c, "Account ID not found in token")
		return
	}

	machineID := c.Param("id")
	if machineID == "" {
		responses.RenderBadRequest(c, "Machine ID is required")
		return
	}

	// Parse machine UUID
	machineUUID, err := uuid.Parse(machineID)
	if err != nil {
		responses.RenderBadRequest(c, "Invalid machine ID format")
		return
	}

	// Get machine
	machine, err := h.serviceCoordinator.Repositories.Machine().GetByID(c.Request.Context(), machineUUID)
	if err != nil {
		responses.RenderNotFound(c, "Machine not found")
		return
	}

	// Verify machine belongs to the account
	if machine.AccountID != accountID.String() {
		responses.RenderNotFound(c, "Machine not found")
		return
	}

	// Reset machine heartbeat (Ruby: sets last_heartbeat_at to nil)
	now := time.Now()
	machine.LastHeartbeatAt = nil
	machine.UpdatedAt = now

	err = h.serviceCoordinator.Repositories.Machine().Update(c.Request.Context(), machine)
	if err != nil {
		responses.RenderInternalError(c, "Failed to reset machine heartbeat: "+err.Error())
		return
	}

	// Broadcast heartbeat reset event
	account, err := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	if err == nil {
		h.serviceCoordinator.Events.BroadcastEvent(
			c.Request.Context(),
			events.EventMachineHeartbeatReset,
			account,
			events.MakeEventResource(machine),
			events.EventMeta{},
		)
	}

	response := gin.H{
		"data": machine,
		"meta": gin.H{
			"ts":         now,
			"reset_time": now,
		},
	}

	c.JSON(http.StatusOK, response)
}

// CheckoutMachineHandler handles machine checkout (Ruby: MachineCheckoutService)
func (mh *MachineHandler) CheckoutMachineHandler(c *gin.Context) {
	machineID := c.Param("id")
	if machineID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Machine ID is required"})
		return
	}

	// Parse query parameters for checkout options
	var options struct {
		Encrypt   bool     `form:"encrypt"`
		Algorithm string   `form:"algorithm"`
		TTL       *int     `form:"ttl"`
		Include   []string `form:"include"`
	}

	if err := c.ShouldBindQuery(&options); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// For POST requests, also check body
	if c.Request.Method == "POST" {
		var req struct {
			Meta *struct {
				Encrypt   *bool    `json:"encrypt,omitempty"`
				Algorithm *string  `json:"algorithm,omitempty"`
				TTL       *int     `json:"ttl,omitempty"`
				Include   []string `json:"include,omitempty"`
			} `json:"meta,omitempty"`
		}

		if err := c.ShouldBindJSON(&req); err == nil && req.Meta != nil {
			if req.Meta.Encrypt != nil {
				options.Encrypt = *req.Meta.Encrypt
			}
			if req.Meta.Algorithm != nil {
				options.Algorithm = *req.Meta.Algorithm
			}
			if req.Meta.TTL != nil {
				options.TTL = req.Meta.TTL
			}
			if len(req.Meta.Include) > 0 {
				options.Include = req.Meta.Include
			}
		}
	}

	// Implement machine checkout using checkout service
	// This should generate a signed machine certificate

	// For GET request, return certificate as attachment
	if c.Request.Method == "GET" {
		certificateData := "PLACEHOLDER_MACHINE_CERTIFICATE_DATA" // Actual certificate from checkout service
		c.Header("Content-Disposition", `attachment; filename="`+machineID+`.lic"`)
		c.Header("Content-Type", "application/octet-stream")
		c.String(http.StatusOK, certificateData)
		return
	}

	// For POST request, return JSON response
	response := gin.H{
		"data": gin.H{
			"id":          machineID,
			"certificate": "PLACEHOLDER_MACHINE_CERTIFICATE_DATA", // Actual certificate from checkout service
			"algorithm":   options.Algorithm,
			"encrypted":   options.Encrypt,
		},
	}

	c.JSON(http.StatusOK, response)
}

// UpdateMachineHandler handles PUT /api/v1/machines/:id
func (mh *MachineHandler) UpdateMachineHandler(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		responses.RenderUnauthorized(c, "Account ID not found in token")
		return
	}

	machineIDStr := c.Param("id")
	machineID, err := uuid.Parse(machineIDStr)
	if err != nil {
		responses.RenderBadRequest(c, "Invalid machine ID format")
		return
	}

	// Get existing machine
	machine, err := mh.serviceCoordinator.Repositories.Machine().GetByID(c.Request.Context(), machineID)
	if err != nil {
		responses.RenderNotFound(c, "Machine not found")
		return
	}

	// Verify machine belongs to the account
	if machine.AccountID != accountID.String() {
		responses.RenderNotFound(c, "Machine not found")
		return
	}

	var req MachineRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responses.RenderBadRequest(c, "Invalid request format: "+err.Error())
		return
	}

	// Update fields
	if req.Data.Attributes.Name != "" {
		machine.Name = req.Data.Attributes.Name
	}
	if req.Data.Attributes.Platform != nil {
		machine.Platform = *req.Data.Attributes.Platform
	}
	if req.Data.Attributes.Hostname != nil {
		machine.Hostname = *req.Data.Attributes.Hostname
	}
	if req.Data.Attributes.IP != nil {
		machine.IP = *req.Data.Attributes.IP
	}
	if req.Data.Attributes.Cores != nil {
		machine.Cores = *req.Data.Attributes.Cores
	}
	if req.Data.Attributes.Metadata != nil {
		machine.Metadata = req.Data.Attributes.Metadata
	}
	machine.UpdatedAt = time.Now()

	// Update machine
	if err := mh.serviceCoordinator.Repositories.Machine().Update(c.Request.Context(), machine); err != nil {
		responses.RenderInternalError(c, "Failed to update machine: "+err.Error())
		return
	}

	// Broadcast event
	account, _ := mh.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	mh.serviceCoordinator.Events.BroadcastEvent(
		c.Request.Context(),
		events.EventMachineUpdated,
		account,
		events.MakeEventResource(machine),
		events.EventMeta{},
	)

	c.JSON(http.StatusOK, gin.H{
		"data": machine,
	})
}

// DeleteMachineHandler handles DELETE /api/v1/machines/:id
func (mh *MachineHandler) DeleteMachineHandler(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		responses.RenderUnauthorized(c, "Account ID not found in token")
		return
	}

	machineIDStr := c.Param("id")
	machineID, err := uuid.Parse(machineIDStr)
	if err != nil {
		responses.RenderBadRequest(c, "Invalid machine ID format")
		return
	}

	// Get existing machine
	machine, err := mh.serviceCoordinator.Repositories.Machine().GetByID(c.Request.Context(), machineID)
	if err != nil {
		responses.RenderNotFound(c, "Machine not found")
		return
	}

	// Verify machine belongs to the account
	if machine.AccountID != accountID.String() {
		responses.RenderNotFound(c, "Machine not found")
		return
	}

	// Delete machine
	if err := mh.serviceCoordinator.Repositories.Machine().Delete(c.Request.Context(), machineID); err != nil {
		responses.RenderInternalError(c, "Failed to delete machine: "+err.Error())
		return
	}

	// Broadcast event
	account, _ := mh.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	mh.serviceCoordinator.Events.BroadcastEvent(
		c.Request.Context(),
		events.EventMachineDeleted,
		account,
		events.MakeEventResource(machine),
		events.EventMeta{},
	)

	c.Status(http.StatusNoContent)
}