package handlers

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/rs/zerolog"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/services"
)

type EntitlementIntegrationTestSuite struct {
	t                  *testing.T
	db                 *gorm.DB
	router             *gin.Engine
	serviceCoordinator *services.ServiceCoordinator
	testAccount        *entities.Account
	testEnvironment    *entities.Environment
	authToken          string
}

func NewEntitlementIntegrationTestSuite(t *testing.T) *EntitlementIntegrationTestSuite {
	return &EntitlementIntegrationTestSuite{t: t}
}

func (suite *EntitlementIntegrationTestSuite) SetupSuite() {
	// Setup test database connection
	dsn := os.Getenv("TEST_DATABASE_URL")
	if dsn == "" {
		dsn = "postgres://postgres:postgres@localhost:5432/gokeys_test?sslmode=disable"
	}

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	require.NoError(suite.t, err, "Failed to connect to test database")

	suite.db = db

	// Create tables manually for entitlement tests to avoid GORM relationship issues
	err = suite.db.Exec(`
		CREATE TABLE IF NOT EXISTS accounts (
			id VARCHAR(36) PRIMARY KEY,
			name VARCHAR(255) NOT NULL,
			slug VARCHAR(255) UNIQUE NOT NULL,
			email VARCHAR(255) UNIQUE NOT NULL,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		);
		
		CREATE TABLE IF NOT EXISTS environments (
			id VARCHAR(36) PRIMARY KEY,
			account_id VARCHAR(36) NOT NULL,
			name VARCHAR(255) NOT NULL,
			code VARCHAR(255) NOT NULL,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		);
		
		CREATE TABLE IF NOT EXISTS entitlements (
			id VARCHAR(36) PRIMARY KEY,
			account_id VARCHAR(36) NOT NULL,
			environment_id VARCHAR(36),
			name VARCHAR(255) NOT NULL,
			code VARCHAR(255) NOT NULL,
			metadata JSONB,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		);
	`).Error
	require.NoError(suite.t, err, "Failed to create test tables")

	// Setup service coordinator
	testLogger := zerolog.New(os.Stdout).With().Timestamp().Logger()
	suite.serviceCoordinator = services.NewServiceCoordinator(suite.db, testLogger)

	// Setup Gin router
	gin.SetMode(gin.TestMode)
	suite.router = gin.New()
	
	// Setup entitlement routes
	entitlementHandler := NewEntitlementHandler(suite.serviceCoordinator)
	api := suite.router.Group("/api/v1")
	{
		entitlements := api.Group("/entitlements")
		{
			entitlements.GET("", entitlementHandler.ListEntitlementsHandler)
			entitlements.POST("", entitlementHandler.CreateEntitlementHandler)
			entitlements.GET("/:id", entitlementHandler.GetEntitlementHandler)
			entitlements.PUT("/:id", entitlementHandler.UpdateEntitlementHandler)
			entitlements.DELETE("/:id", entitlementHandler.DeleteEntitlementHandler)
		}
	}
}

func (suite *EntitlementIntegrationTestSuite) SetupTest() {
	// Clean up test data
	suite.db.Exec("DELETE FROM entitlements")
	suite.db.Exec("DELETE FROM environments")
	suite.db.Exec("DELETE FROM accounts")

	// Create test account
	suite.testAccount = &entities.Account{
		ID:        uuid.New().String(),
		Name:      "Test Account",
		Slug:      "test-account",
		Email:     "<EMAIL>",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
	err := suite.serviceCoordinator.Repositories.Account().Create(context.Background(), suite.testAccount)
	require.NoError(suite.t, err)

	// Create test environment
	suite.testEnvironment = &entities.Environment{
		ID:                uuid.New().String(),
		AccountID:         suite.testAccount.ID,
		Name:              "Test Environment",
		Code:              "test",
		IsolationStrategy: "SHARED",
		CreatedAt:         time.Now(),
		UpdatedAt:         time.Now(),
	}
	err = suite.serviceCoordinator.Repositories.Environment().Create(context.Background(), suite.testEnvironment)
	require.NoError(suite.t, err)

	// Setup auth token (mock JWT token for testing)
	suite.authToken = "Bearer test-token"
}

func (suite *EntitlementIntegrationTestSuite) TearDownSuite() {
	// Clean up database
	if suite.db != nil {
		suite.db.Exec("DROP TABLE IF EXISTS entitlements CASCADE")
		suite.db.Exec("DROP TABLE IF EXISTS environments CASCADE")
		suite.db.Exec("DROP TABLE IF EXISTS accounts CASCADE")
		
		sqlDB, _ := suite.db.DB()
		sqlDB.Close()
	}
}

// Mock middleware to set account ID in context
func mockAccountMiddleware(accountID string) gin.HandlerFunc {
	return func(c *gin.Context) {
		if accountID != "" {
			accountUUID, _ := uuid.Parse(accountID)
			c.Set("account_id", accountUUID)
		}
		c.Next()
	}
}

func (suite *EntitlementIntegrationTestSuite) makeRequest(method, path string, body interface{}, accountID string) *httptest.ResponseRecorder {
	var buf bytes.Buffer
	if body != nil {
		err := json.NewEncoder(&buf).Encode(body)
		require.NoError(suite.t, err)
	}

	req := httptest.NewRequest(method, path, &buf)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", suite.authToken)
	
	// Create a new router instance with account middleware for this request
	router := gin.New()
	entitlementHandler := NewEntitlementHandler(suite.serviceCoordinator)
	
	api := router.Group("/api/v1")
	if accountID != "" {
		api.Use(mockAccountMiddleware(accountID))
	}
	{
		entitlements := api.Group("/entitlements")
		{
			entitlements.GET("", entitlementHandler.ListEntitlementsHandler)
			entitlements.POST("", entitlementHandler.CreateEntitlementHandler)
			entitlements.GET("/:id", entitlementHandler.GetEntitlementHandler)
			entitlements.PUT("/:id", entitlementHandler.UpdateEntitlementHandler)
			entitlements.DELETE("/:id", entitlementHandler.DeleteEntitlementHandler)
		}
	}

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)
	return w
}

func (suite *EntitlementIntegrationTestSuite) TestCreateEntitlement() {
	requestBody := map[string]interface{}{
		"data": map[string]interface{}{
			"type": "entitlements",
			"attributes": map[string]interface{}{
				"name":     "Test Entitlement",
				"code":     "TEST_FEATURE",
				"metadata": map[string]interface{}{
					"description": "Test feature entitlement",
				},
			},
			"relationships": map[string]interface{}{
				"environment": map[string]interface{}{
					"data": map[string]interface{}{
						"type": "environments",
						"id":   suite.testEnvironment.ID,
					},
				},
			},
		},
	}

	w := suite.makeRequest("POST", "/api/v1/entitlements", requestBody, suite.testAccount.ID)

	assert.Equal(suite.t, http.StatusCreated, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.t, err)

	data := response["data"].(map[string]interface{})
	assert.Equal(suite.t, "Test Entitlement", data["name"])
	assert.Equal(suite.t, "TEST_FEATURE", data["code"])
	assert.Equal(suite.t, suite.testAccount.ID, data["account_id"])
	assert.NotEmpty(suite.t, data["id"])

	// Verify Location header
	expectedLocation := fmt.Sprintf("/api/v1/entitlements/%s", data["id"])
	assert.Equal(suite.t, expectedLocation, w.Header().Get("Location"))

	// Verify entitlement was created in database
	var entitlement entities.Entitlement
	err = suite.db.Where("id = ?", data["id"]).First(&entitlement).Error
	require.NoError(suite.t, err)
	assert.Equal(suite.t, "Test Entitlement", entitlement.Name)
	assert.Equal(suite.t, "TEST_FEATURE", entitlement.Code)
}

func (suite *EntitlementIntegrationTestSuite) TestCreateEntitlementValidation() {
	testCases := []struct {
		name           string
		requestBody    map[string]interface{}
		expectedStatus int
		expectedError  string
	}{
		{
			name: "missing name",
			requestBody: map[string]interface{}{
				"data": map[string]interface{}{
					"type": "entitlements",
					"attributes": map[string]interface{}{
						"code": "TEST_FEATURE",
					},
				},
			},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "name",
		},
		{
			name: "missing code",
			requestBody: map[string]interface{}{
				"data": map[string]interface{}{
					"type": "entitlements",
					"attributes": map[string]interface{}{
						"name": "Test Entitlement",
					},
				},
			},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "code",
		},
		{
			name: "missing type",
			requestBody: map[string]interface{}{
				"data": map[string]interface{}{
					"attributes": map[string]interface{}{
						"name": "Test Entitlement",
						"code": "TEST_FEATURE",
					},
				},
			},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "type",
		},
	}

	for _, tc := range testCases {
		suite.t.Run(tc.name, func(t *testing.T) {
			w := suite.makeRequest("POST", "/api/v1/entitlements", tc.requestBody, suite.testAccount.ID)
			assert.Equal(t, tc.expectedStatus, w.Code)
			
			var response map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &response)
			require.NoError(t, err)
			
			errorMsg := response["error"].(string)
			assert.Contains(t, errorMsg, tc.expectedError)
		})
	}
}

func (suite *EntitlementIntegrationTestSuite) TestGetEntitlement() {
	// Create test entitlement
	entitlement := &entities.Entitlement{
		ID:            uuid.New().String(),
		AccountID:     suite.testAccount.ID,
		EnvironmentID: &suite.testEnvironment.ID,
		Name:          "Get Test Entitlement",
		Code:          "GET_TEST",
		Metadata: map[string]interface{}{
			"description": "Test get entitlement",
		},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
	err := suite.serviceCoordinator.Repositories.Entitlement().Create(context.Background(), entitlement)
	require.NoError(suite.t, err)

	w := suite.makeRequest("GET", fmt.Sprintf("/api/v1/entitlements/%s", entitlement.ID), nil, suite.testAccount.ID)

	assert.Equal(suite.t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.t, err)

	data := response["data"].(map[string]interface{})
	assert.Equal(suite.t, entitlement.ID, data["id"])
	assert.Equal(suite.t, "Get Test Entitlement", data["name"])
	assert.Equal(suite.t, "GET_TEST", data["code"])
	assert.Equal(suite.t, suite.testAccount.ID, data["account_id"])
}

func (suite *EntitlementIntegrationTestSuite) TestGetEntitlementNotFound() {
	nonExistentID := uuid.New().String()
	w := suite.makeRequest("GET", fmt.Sprintf("/api/v1/entitlements/%s", nonExistentID), nil, suite.testAccount.ID)

	assert.Equal(suite.t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.t, err)

	assert.Equal(suite.t, "Entitlement not found", response["error"])
}

func (suite *EntitlementIntegrationTestSuite) TestDeleteEntitlement() {
	// Create test entitlement
	entitlement := &entities.Entitlement{
		ID:        uuid.New().String(),
		AccountID: suite.testAccount.ID,
		Name:      "To Delete",
		Code:      "DELETE_TEST",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
	err := suite.serviceCoordinator.Repositories.Entitlement().Create(context.Background(), entitlement)
	require.NoError(suite.t, err)

	w := suite.makeRequest("DELETE", fmt.Sprintf("/api/v1/entitlements/%s", entitlement.ID), nil, suite.testAccount.ID)

	assert.Equal(suite.t, http.StatusNoContent, w.Code)
	assert.Empty(suite.t, w.Body.String())

	// Verify entitlement was deleted from database
	var deletedEntitlement entities.Entitlement
	err = suite.db.Where("id = ?", entitlement.ID).First(&deletedEntitlement).Error
	assert.Error(suite.t, err)
	assert.Equal(suite.t, gorm.ErrRecordNotFound, err)
}

func (suite *EntitlementIntegrationTestSuite) TestListEntitlements() {
	// Create multiple test entitlements
	entitlements := []*entities.Entitlement{
		{
			ID:        uuid.New().String(),
			AccountID: suite.testAccount.ID,
			Name:      "Entitlement 1",
			Code:      "ENT_1",
			CreatedAt: time.Now().Add(-2 * time.Hour),
			UpdatedAt: time.Now().Add(-2 * time.Hour),
		},
		{
			ID:        uuid.New().String(),
			AccountID: suite.testAccount.ID,
			Name:      "Entitlement 2",
			Code:      "ENT_2",
			CreatedAt: time.Now().Add(-1 * time.Hour),
			UpdatedAt: time.Now().Add(-1 * time.Hour),
		},
		{
			ID:        uuid.New().String(),
			AccountID: suite.testAccount.ID,
			Name:      "Entitlement 3",
			Code:      "ENT_3",
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
	}

	for _, ent := range entitlements {
		err := suite.serviceCoordinator.Repositories.Entitlement().Create(context.Background(), ent)
		require.NoError(suite.t, err)
	}

	w := suite.makeRequest("GET", "/api/v1/entitlements", nil, suite.testAccount.ID)

	assert.Equal(suite.t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.t, err)

	data := response["data"].([]interface{})
	meta := response["meta"].(map[string]interface{})

	assert.Len(suite.t, data, 3)
	assert.Equal(suite.t, float64(1), meta["page"])
	assert.Equal(suite.t, float64(25), meta["limit"])
	assert.Equal(suite.t, float64(3), meta["total"])

	// Verify sorting (newest first)
	firstItem := data[0].(map[string]interface{})
	assert.Equal(suite.t, "Entitlement 3", firstItem["name"])
}

func (suite *EntitlementIntegrationTestSuite) TestUnauthorizedAccess() {
	// Test without account ID in context
	w := suite.makeRequest("GET", "/api/v1/entitlements", nil, "")

	assert.Equal(suite.t, http.StatusUnauthorized, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.t, err)

	assert.Equal(suite.t, "Account not found", response["error"])
}

func (suite *EntitlementIntegrationTestSuite) TestUpdateEntitlement() {
	// Create test entitlement
	entitlement := &entities.Entitlement{
		ID:        uuid.New().String(),
		AccountID: suite.testAccount.ID,
		Name:      "Original Name",
		Code:      "ORIGINAL_CODE",
		Metadata: map[string]interface{}{
			"description": "Original description",
		},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
	err := suite.serviceCoordinator.Repositories.Entitlement().Create(context.Background(), entitlement)
	require.NoError(suite.t, err)

	requestBody := map[string]interface{}{
		"data": map[string]interface{}{
			"type": "entitlements",
			"attributes": map[string]interface{}{
				"name": "Updated Name",
				"code": "UPDATED_CODE",
				"metadata": map[string]interface{}{
					"description": "Updated description",
					"new_field":   "new value",
				},
			},
			"relationships": map[string]interface{}{
				"environment": map[string]interface{}{
					"data": map[string]interface{}{
						"type": "environments",
						"id":   suite.testEnvironment.ID,
					},
				},
			},
		},
	}

	w := suite.makeRequest("PUT", fmt.Sprintf("/api/v1/entitlements/%s", entitlement.ID), requestBody, suite.testAccount.ID)

	assert.Equal(suite.t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.t, err)

	data := response["data"].(map[string]interface{})
	assert.Equal(suite.t, "Updated Name", data["name"])
	assert.Equal(suite.t, "UPDATED_CODE", data["code"])
	assert.Equal(suite.t, suite.testAccount.ID, data["account_id"])

	// Verify entitlement was updated in database
	var updatedEntitlement entities.Entitlement
	err = suite.db.Where("id = ?", entitlement.ID).First(&updatedEntitlement).Error
	require.NoError(suite.t, err)
	assert.Equal(suite.t, "Updated Name", updatedEntitlement.Name)
	assert.Equal(suite.t, "UPDATED_CODE", updatedEntitlement.Code)
	assert.Equal(suite.t, "Updated description", updatedEntitlement.Metadata["description"])
	assert.Equal(suite.t, "new value", updatedEntitlement.Metadata["new_field"])
}

func (suite *EntitlementIntegrationTestSuite) TestUpdateEntitlementNotFound() {
	nonExistentID := uuid.New().String()
	requestBody := map[string]interface{}{
		"data": map[string]interface{}{
			"type": "entitlements",
			"attributes": map[string]interface{}{
				"name": "Updated Name",
				"code": "UPDATED_CODE",
			},
		},
	}

	w := suite.makeRequest("PUT", fmt.Sprintf("/api/v1/entitlements/%s", nonExistentID), requestBody, suite.testAccount.ID)

	assert.Equal(suite.t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.t, err)

	assert.Equal(suite.t, "Entitlement not found", response["error"])
}

func (suite *EntitlementIntegrationTestSuite) TestListEntitlementsPagination() {
	// Create 30 test entitlements
	for i := 1; i <= 30; i++ {
		entitlement := &entities.Entitlement{
			ID:        uuid.New().String(),
			AccountID: suite.testAccount.ID,
			Name:      fmt.Sprintf("Entitlement %d", i),
			Code:      fmt.Sprintf("ENT_%d", i),
			CreatedAt: time.Now().Add(-time.Duration(30-i) * time.Minute),
			UpdatedAt: time.Now().Add(-time.Duration(30-i) * time.Minute),
		}
		err := suite.serviceCoordinator.Repositories.Entitlement().Create(context.Background(), entitlement)
		require.NoError(suite.t, err)
	}

	// Test first page
	w := suite.makeRequest("GET", "/api/v1/entitlements?page=1&limit=10", nil, suite.testAccount.ID)
	assert.Equal(suite.t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.t, err)

	data := response["data"].([]interface{})
	meta := response["meta"].(map[string]interface{})

	assert.Len(suite.t, data, 10)
	assert.Equal(suite.t, float64(1), meta["page"])
	assert.Equal(suite.t, float64(10), meta["limit"])
	assert.Equal(suite.t, float64(30), meta["total"])

	// Test second page
	w = suite.makeRequest("GET", "/api/v1/entitlements?page=2&limit=10", nil, suite.testAccount.ID)
	assert.Equal(suite.t, http.StatusOK, w.Code)

	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.t, err)

	data = response["data"].([]interface{})
	meta = response["meta"].(map[string]interface{})

	assert.Len(suite.t, data, 10)
	assert.Equal(suite.t, float64(2), meta["page"])
	assert.Equal(suite.t, float64(10), meta["limit"])
	assert.Equal(suite.t, float64(30), meta["total"])
}

func (suite *EntitlementIntegrationTestSuite) TestCreateEntitlementWithComplexMetadata() {
	requestBody := map[string]interface{}{
		"data": map[string]interface{}{
			"type": "entitlements",
			"attributes": map[string]interface{}{
				"name": "Complex Metadata Entitlement",
				"code": "COMPLEX_META",
				"metadata": map[string]interface{}{
					"description":    "Test with complex metadata",
					"version":        "1.2.3",
					"features":       []string{"feature1", "feature2", "feature3"},
					"limits":         map[string]interface{}{"max_users": 100, "max_requests": 1000},
					"configuration":  map[string]interface{}{"enabled": true, "debug": false},
					"tags":          []string{"premium", "enterprise"},
					"created_by":    "test-user",
					"settings": map[string]interface{}{
						"notifications": map[string]interface{}{
							"email": true,
							"sms":   false,
						},
						"security": map[string]interface{}{
							"two_factor": true,
							"ip_whitelist": []string{"***********/24", "10.0.0.0/8"},
						},
					},
				},
			},
		},
	}

	w := suite.makeRequest("POST", "/api/v1/entitlements", requestBody, suite.testAccount.ID)

	assert.Equal(suite.t, http.StatusCreated, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.t, err)

	data := response["data"].(map[string]interface{})
	assert.Equal(suite.t, "Complex Metadata Entitlement", data["name"])
	assert.Equal(suite.t, "COMPLEX_META", data["code"])

	// Verify complex metadata was stored correctly
	metadata := data["metadata"].(map[string]interface{})
	assert.Equal(suite.t, "1.2.3", metadata["version"])
	assert.Contains(suite.t, metadata["features"], "feature1")
	
	limits := metadata["limits"].(map[string]interface{})
	assert.Equal(suite.t, float64(100), limits["max_users"])
	
	settings := metadata["settings"].(map[string]interface{})
	notifications := settings["notifications"].(map[string]interface{})
	assert.Equal(suite.t, true, notifications["email"])

	// Verify in database
	var entitlement entities.Entitlement
	err = suite.db.Where("id = ?", data["id"]).First(&entitlement).Error
	require.NoError(suite.t, err)
	assert.Equal(suite.t, "1.2.3", entitlement.Metadata["version"])
}

func (suite *EntitlementIntegrationTestSuite) TestConcurrentOperations() {
	// Test concurrent creation of entitlements
	numGoroutines := 10
	done := make(chan string, numGoroutines)
	var createdIDs []string

	for i := 0; i < numGoroutines; i++ {
		go func(index int) {
			requestBody := map[string]interface{}{
				"data": map[string]interface{}{
					"type": "entitlements",
					"attributes": map[string]interface{}{
						"name": fmt.Sprintf("Concurrent Entitlement %d", index),
						"code": fmt.Sprintf("CONCURRENT_%d", index),
					},
				},
			}

			w := suite.makeRequest("POST", "/api/v1/entitlements", requestBody, suite.testAccount.ID)
			assert.Equal(suite.t, http.StatusCreated, w.Code)

			var response map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &response)
			require.NoError(suite.t, err)

			data := response["data"].(map[string]interface{})
			done <- data["id"].(string)
		}(i)
	}

	// Wait for all goroutines to complete and collect IDs
	for i := 0; i < numGoroutines; i++ {
		id := <-done
		createdIDs = append(createdIDs, id)
	}

	// Verify all entitlements were created
	var count int64
	err := suite.db.Model(&entities.Entitlement{}).Where("account_id = ?", suite.testAccount.ID).Count(&count).Error
	require.NoError(suite.t, err)
	assert.Equal(suite.t, int64(numGoroutines), count)

	// Verify all IDs are unique
	uniqueIDs := make(map[string]bool)
	for _, id := range createdIDs {
		assert.False(suite.t, uniqueIDs[id], "Duplicate ID found: %s", id)
		uniqueIDs[id] = true
	}
	assert.Len(suite.t, uniqueIDs, numGoroutines)
}

func (suite *EntitlementIntegrationTestSuite) TestErrorRecovery() {
	suite.t.Run("invalid_json_recovery", func(t *testing.T) {
		req := httptest.NewRequest("POST", "/api/v1/entitlements", bytes.NewBuffer([]byte("invalid json")))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", suite.authToken)

		router := gin.New()
		entitlementHandler := NewEntitlementHandler(suite.serviceCoordinator)
		api := router.Group("/api/v1")
		api.Use(mockAccountMiddleware(suite.testAccount.ID))
		{
			entitlements := api.Group("/entitlements")
			entitlements.POST("", entitlementHandler.CreateEntitlementHandler)
		}

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	suite.t.Run("malformed_request_recovery", func(t *testing.T) {
		malformedBody := map[string]interface{}{
			"invalid_structure": "test",
		}

		w := suite.makeRequest("POST", "/api/v1/entitlements", malformedBody, suite.testAccount.ID)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	suite.t.Run("database_constraint_recovery", func(t *testing.T) {
		// Create entitlement with specific code
		entitlement := &entities.Entitlement{
			ID:        uuid.New().String(),
			AccountID: suite.testAccount.ID,
			Name:      "Original",
			Code:      "UNIQUE_CODE",
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		err := suite.serviceCoordinator.Repositories.Entitlement().Create(context.Background(), entitlement)
		require.NoError(suite.t, err)

		// Try to create another entitlement with same code (if unique constraint exists)
		requestBody := map[string]interface{}{
			"data": map[string]interface{}{
				"type": "entitlements",
				"attributes": map[string]interface{}{
					"name": "Duplicate Code Test",
					"code": "UNIQUE_CODE",
				},
			},
		}

		w := suite.makeRequest("POST", "/api/v1/entitlements", requestBody, suite.testAccount.ID)
		// This might succeed or fail depending on whether unique constraints are enforced
		// The test ensures the handler doesn't crash
		assert.True(suite.t, w.Code == http.StatusCreated || w.Code == http.StatusUnprocessableEntity)
	})
}

func (suite *EntitlementIntegrationTestSuite) TestContentTypeValidation() {
	requestBody := map[string]interface{}{
		"data": map[string]interface{}{
			"type": "entitlements",
			"attributes": map[string]interface{}{
				"name": "Content Type Test",
				"code": "CONTENT_TYPE_TEST",
			},
		},
	}

	body, _ := json.Marshal(requestBody)

	suite.t.Run("missing_content_type", func(t *testing.T) {
		req := httptest.NewRequest("POST", "/api/v1/entitlements", bytes.NewBuffer(body))
		req.Header.Set("Authorization", suite.authToken)
		// Missing Content-Type header

		router := gin.New()
		entitlementHandler := NewEntitlementHandler(suite.serviceCoordinator)
		api := router.Group("/api/v1")
		api.Use(mockAccountMiddleware(suite.testAccount.ID))
		{
			entitlements := api.Group("/entitlements")
			entitlements.POST("", entitlementHandler.CreateEntitlementHandler)
		}

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Should handle missing content type gracefully
		assert.True(t, w.Code == http.StatusBadRequest || w.Code == http.StatusCreated)
	})

	suite.t.Run("wrong_content_type", func(t *testing.T) {
		req := httptest.NewRequest("POST", "/api/v1/entitlements", bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "text/plain")
		req.Header.Set("Authorization", suite.authToken)

		router := gin.New()
		entitlementHandler := NewEntitlementHandler(suite.serviceCoordinator)
		api := router.Group("/api/v1")
		api.Use(mockAccountMiddleware(suite.testAccount.ID))
		{
			entitlements := api.Group("/entitlements")
			entitlements.POST("", entitlementHandler.CreateEntitlementHandler)
		}

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Should handle wrong content type gracefully
		assert.True(t, w.Code == http.StatusBadRequest || w.Code == http.StatusCreated)
	})
}

func (suite *EntitlementIntegrationTestSuite) TestCompleteWorkflow() {
	// End-to-end workflow test
	var entitlementID string

	// Step 1: Create entitlement
	suite.t.Run("create", func(t *testing.T) {
		requestBody := map[string]interface{}{
			"data": map[string]interface{}{
				"type": "entitlements",
				"attributes": map[string]interface{}{
					"name": "Workflow Test Entitlement",
					"code": "WORKFLOW_TEST",
					"metadata": map[string]interface{}{
						"version": "1.0",
					},
				},
			},
		}

		w := suite.makeRequest("POST", "/api/v1/entitlements", requestBody, suite.testAccount.ID)
		assert.Equal(t, http.StatusCreated, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		data := response["data"].(map[string]interface{})
		entitlementID = data["id"].(string)
		assert.NotEmpty(t, entitlementID)
	})

	// Step 2: Get entitlement
	suite.t.Run("read", func(t *testing.T) {
		w := suite.makeRequest("GET", fmt.Sprintf("/api/v1/entitlements/%s", entitlementID), nil, suite.testAccount.ID)
		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		data := response["data"].(map[string]interface{})
		assert.Equal(t, "Workflow Test Entitlement", data["name"])
		assert.Equal(t, "WORKFLOW_TEST", data["code"])
	})

	// Step 3: Update entitlement
	suite.t.Run("update", func(t *testing.T) {
		requestBody := map[string]interface{}{
			"data": map[string]interface{}{
				"type": "entitlements",
				"attributes": map[string]interface{}{
					"name": "Updated Workflow Test Entitlement",
					"code": "UPDATED_WORKFLOW_TEST",
					"metadata": map[string]interface{}{
						"version": "2.0",
						"updated": true,
					},
				},
			},
		}

		w := suite.makeRequest("PUT", fmt.Sprintf("/api/v1/entitlements/%s", entitlementID), requestBody, suite.testAccount.ID)
		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		data := response["data"].(map[string]interface{})
		assert.Equal(t, "Updated Workflow Test Entitlement", data["name"])
		assert.Equal(t, "UPDATED_WORKFLOW_TEST", data["code"])
	})

	// Step 4: List entitlements (should include updated one)
	suite.t.Run("list", func(t *testing.T) {
		w := suite.makeRequest("GET", "/api/v1/entitlements", nil, suite.testAccount.ID)
		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		data := response["data"].([]interface{})
		assert.Len(t, data, 1)

		firstItem := data[0].(map[string]interface{})
		assert.Equal(t, "Updated Workflow Test Entitlement", firstItem["name"])
	})

	// Step 5: Delete entitlement
	suite.t.Run("delete", func(t *testing.T) {
		w := suite.makeRequest("DELETE", fmt.Sprintf("/api/v1/entitlements/%s", entitlementID), nil, suite.testAccount.ID)
		assert.Equal(t, http.StatusNoContent, w.Code)
	})

	// Step 6: Verify deletion
	suite.t.Run("verify_deletion", func(t *testing.T) {
		w := suite.makeRequest("GET", fmt.Sprintf("/api/v1/entitlements/%s", entitlementID), nil, suite.testAccount.ID)
		assert.Equal(t, http.StatusNotFound, w.Code)
	})
}

// TestEntitlementIntegrationSuite runs the integration test suite
func TestEntitlementIntegrationSuite(t *testing.T) {
	// Skip integration tests if not in integration test mode
	if testing.Short() {
		t.Skip("Skipping integration tests in short mode")
	}

	suite := NewEntitlementIntegrationTestSuite(t)
	
	// Run setup
	suite.SetupSuite()
	defer suite.TearDownSuite()

	// Run individual tests  
	t.Run("SetupTest", func(t *testing.T) { suite.SetupTest() })
	t.Run("TestCreateEntitlement", func(t *testing.T) { suite.TestCreateEntitlement() })
	t.Run("TestCreateEntitlementValidation", func(t *testing.T) { suite.TestCreateEntitlementValidation() })
	t.Run("TestCreateEntitlementWithComplexMetadata", func(t *testing.T) { suite.TestCreateEntitlementWithComplexMetadata() })
	t.Run("TestGetEntitlement", func(t *testing.T) { suite.TestGetEntitlement() })
	t.Run("TestGetEntitlementNotFound", func(t *testing.T) { suite.TestGetEntitlementNotFound() })
	t.Run("TestUpdateEntitlement", func(t *testing.T) { suite.TestUpdateEntitlement() })
	t.Run("TestUpdateEntitlementNotFound", func(t *testing.T) { suite.TestUpdateEntitlementNotFound() })
	t.Run("TestDeleteEntitlement", func(t *testing.T) { suite.TestDeleteEntitlement() })
	t.Run("TestListEntitlements", func(t *testing.T) { suite.TestListEntitlements() })
	t.Run("TestListEntitlementsPagination", func(t *testing.T) { suite.TestListEntitlementsPagination() })
	t.Run("TestUnauthorizedAccess", func(t *testing.T) { suite.TestUnauthorizedAccess() })
	t.Run("TestConcurrentOperations", func(t *testing.T) { suite.TestConcurrentOperations() })
	t.Run("TestErrorRecovery", func(t *testing.T) { suite.TestErrorRecovery() })
	t.Run("TestContentTypeValidation", func(t *testing.T) { suite.TestContentTypeValidation() })
	t.Run("TestInvalidUUIDHandling", func(t *testing.T) { suite.TestInvalidUUIDHandling() })
	t.Run("TestLargeMetadataHandling", func(t *testing.T) { suite.TestLargeMetadataHandling() })
	t.Run("TestSpecialCharactersInFields", func(t *testing.T) { suite.TestSpecialCharactersInFields() })
	t.Run("TestEmptyAndNullValues", func(t *testing.T) { suite.TestEmptyAndNullValues() })
	t.Run("TestAccountIsolation", func(t *testing.T) { suite.TestAccountIsolation() })
	t.Run("TestRateLimitingAndPerformance", func(t *testing.T) { suite.TestRateLimitingAndPerformance() })
	t.Run("TestCompleteWorkflow", func(t *testing.T) { suite.TestCompleteWorkflow() })
}

func (suite *EntitlementIntegrationTestSuite) TestInvalidUUIDHandling() {
	invalidUUIDs := []string{
		"invalid-uuid",
		"12345",
		"not-a-uuid-at-all",
		"",
		"********-0000-0000-0000-************", // Nil UUID
	}

	for _, invalidID := range invalidUUIDs {
		suite.t.Run(fmt.Sprintf("invalid_uuid_%s", invalidID), func(t *testing.T) {
			w := suite.makeRequest("GET", fmt.Sprintf("/api/v1/entitlements/%s", invalidID), nil, suite.testAccount.ID)
			assert.Equal(t, http.StatusBadRequest, w.Code)

			var response map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &response)
			require.NoError(t, err)
			assert.Contains(t, response["error"].(string), "Invalid entitlement ID format")
		})
	}
}

func (suite *EntitlementIntegrationTestSuite) TestLargeMetadataHandling() {
	// Test with large metadata payload
	largeMetadata := make(map[string]interface{})
	
	// Create large nested structure
	for i := 0; i < 100; i++ {
		largeMetadata[fmt.Sprintf("field_%d", i)] = map[string]interface{}{
			"description": fmt.Sprintf("This is a long description for field %d that contains a lot of text to test large metadata handling", i),
			"values":      []string{fmt.Sprintf("value1_%d", i), fmt.Sprintf("value2_%d", i), fmt.Sprintf("value3_%d", i)},
			"config": map[string]interface{}{
				"enabled":    true,
				"priority":   i,
				"parameters": []int{i, i * 2, i * 3, i * 4, i * 5},
			},
		}
	}

	requestBody := map[string]interface{}{
		"data": map[string]interface{}{
			"type": "entitlements",
			"attributes": map[string]interface{}{
				"name":     "Large Metadata Test",
				"code":     "LARGE_META_TEST",
				"metadata": largeMetadata,
			},
		},
	}

	w := suite.makeRequest("POST", "/api/v1/entitlements", requestBody, suite.testAccount.ID)
	assert.Equal(suite.t, http.StatusCreated, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.t, err)

	data := response["data"].(map[string]interface{})
	responseMetadata := data["metadata"].(map[string]interface{})
	
	// Verify some of the large metadata was stored correctly
	assert.Contains(suite.t, responseMetadata, "field_0")
	assert.Contains(suite.t, responseMetadata, "field_50")
	assert.Contains(suite.t, responseMetadata, "field_99")
}

func (suite *EntitlementIntegrationTestSuite) TestSpecialCharactersInFields() {
	testCases := []struct {
		name string
		code string
		desc string
	}{
		{
			name: "Test with émojis 🚀 and unicodé",
			code: "UNICODE_TEST_🚀",
			desc: "Special characters test",
		},
		{
			name: "Test with quotes \"and\" 'apostrophes'",
			code: "QUOTES_TEST",
			desc: "Testing quotes handling",
		},
		{
			name: "Test with newlines\nand\ttabs",
			code: "NEWLINE_TEST",
			desc: "Testing newlines and tabs",
		},
		{
			name: "Test with HTML <script>alert('xss')</script>",
			code: "HTML_TEST",
			desc: "Testing HTML injection",
		},
	}

	for _, tc := range testCases {
		suite.t.Run(fmt.Sprintf("special_chars_%s", tc.code), func(t *testing.T) {
			requestBody := map[string]interface{}{
				"data": map[string]interface{}{
					"type": "entitlements",
					"attributes": map[string]interface{}{
						"name": tc.name,
						"code": tc.code,
						"metadata": map[string]interface{}{
							"description": tc.desc,
						},
					},
				},
			}

			w := suite.makeRequest("POST", "/api/v1/entitlements", requestBody, suite.testAccount.ID)
			assert.Equal(t, http.StatusCreated, w.Code)

			var response map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &response)
			require.NoError(t, err)

			data := response["data"].(map[string]interface{})
			assert.Equal(t, tc.name, data["name"])
			assert.Equal(t, tc.code, data["code"])
		})
	}
}

func (suite *EntitlementIntegrationTestSuite) TestEmptyAndNullValues() {
	testCases := []struct {
		name           string
		requestBody    map[string]interface{}
		expectedStatus int
	}{
		{
			name: "empty_strings",
			requestBody: map[string]interface{}{
				"data": map[string]interface{}{
					"type": "entitlements",
					"attributes": map[string]interface{}{
						"name": "",
						"code": "",
					},
				},
			},
			expectedStatus: http.StatusBadRequest, // Should fail validation
		},
		{
			name: "null_metadata",
			requestBody: map[string]interface{}{
				"data": map[string]interface{}{
					"type": "entitlements",
					"attributes": map[string]interface{}{
						"name":     "Null Metadata Test",
						"code":     "NULL_META_TEST",
						"metadata": nil,
					},
				},
			},
			expectedStatus: http.StatusCreated, // Should succeed with null metadata
		},
		{
			name: "empty_metadata",
			requestBody: map[string]interface{}{
				"data": map[string]interface{}{
					"type": "entitlements",
					"attributes": map[string]interface{}{
						"name":     "Empty Metadata Test",
						"code":     "EMPTY_META_TEST",
						"metadata": map[string]interface{}{},
					},
				},
			},
			expectedStatus: http.StatusCreated, // Should succeed with empty metadata
		},
	}

	for _, tc := range testCases {
		suite.t.Run(tc.name, func(t *testing.T) {
			w := suite.makeRequest("POST", "/api/v1/entitlements", tc.requestBody, suite.testAccount.ID)
			assert.Equal(t, tc.expectedStatus, w.Code)
		})
	}
}

func (suite *EntitlementIntegrationTestSuite) TestAccountIsolation() {
	// Create another test account
	secondAccount := &entities.Account{
		ID:        uuid.New().String(),
		Name:      "Second Test Account",
		Slug:      "second-test-account",
		Email:     "<EMAIL>",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
	err := suite.serviceCoordinator.Repositories.Account().Create(context.Background(), secondAccount)
	require.NoError(suite.t, err)

	// Create entitlement in first account
	entitlement := &entities.Entitlement{
		ID:        uuid.New().String(),
		AccountID: suite.testAccount.ID,
		Name:      "Account 1 Entitlement",
		Code:      "ACCOUNT_1_ENT",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
	err = suite.serviceCoordinator.Repositories.Entitlement().Create(context.Background(), entitlement)
	require.NoError(suite.t, err)

	// Try to access entitlement from second account
	suite.t.Run("cross_account_access_denied", func(t *testing.T) {
		w := suite.makeRequest("GET", fmt.Sprintf("/api/v1/entitlements/%s", entitlement.ID), nil, secondAccount.ID)
		assert.Equal(t, http.StatusNotFound, w.Code) // Should not find it due to account isolation
	})

	// List entitlements from second account should be empty
	suite.t.Run("cross_account_list_isolation", func(t *testing.T) {
		w := suite.makeRequest("GET", "/api/v1/entitlements", nil, secondAccount.ID)
		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		data := response["data"].([]interface{})
		assert.Len(t, data, 0) // Should be empty for second account
	})
}

func (suite *EntitlementIntegrationTestSuite) TestRateLimitingAndPerformance() {
	// Test rapid consecutive requests
	numRequests := 50
	responses := make(chan int, numRequests)

	for i := 0; i < numRequests; i++ {
		go func(index int) {
			requestBody := map[string]interface{}{
				"data": map[string]interface{}{
					"type": "entitlements",
					"attributes": map[string]interface{}{
						"name": fmt.Sprintf("Rapid Request %d", index),
						"code": fmt.Sprintf("RAPID_%d", index),
					},
				},
			}

			w := suite.makeRequest("POST", "/api/v1/entitlements", requestBody, suite.testAccount.ID)
			responses <- w.Code
		}(i)
	}

	// Collect all responses
	successCount := 0
	for i := 0; i < numRequests; i++ {
		code := <-responses
		if code == http.StatusCreated {
			successCount++
		}
	}

	// Most requests should succeed (adjust threshold based on rate limiting implementation)
	assert.Greater(suite.t, successCount, numRequests/2, "At least half of rapid requests should succeed")
}


// Benchmark tests
func BenchmarkEntitlementOperations(b *testing.B) {
	// Setup similar to test suite but for benchmarking
	dsn := os.Getenv("TEST_DATABASE_URL")
	if dsn == "" {
		dsn = "postgres://postgres:postgres@localhost:5432/gokeys_test?sslmode=disable"
	}

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	require.NoError(b, err)

	// Auto-migrate
	err = db.AutoMigrate(&entities.Account{}, &entities.Entitlement{})
	require.NoError(b, err)

	// Setup service coordinator
	testLogger := zerolog.New(os.Stdout).With().Timestamp().Logger()
	serviceCoordinator := services.NewServiceCoordinator(db, testLogger)

	// Create test account
	testAccount := &entities.Account{
		ID:        uuid.New().String(),
		Name:      "Benchmark Account",
		Slug:      "benchmark-account",
		Email:     "<EMAIL>",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
	err = serviceCoordinator.Repositories.Account().Create(context.Background(), testAccount)
	require.NoError(b, err)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.Use(mockAccountMiddleware(testAccount.ID))
	entitlementHandler := NewEntitlementHandler(serviceCoordinator)
	router.POST("/entitlements", entitlementHandler.CreateEntitlementHandler)

	b.ResetTimer()
	
	b.Run("CreateEntitlement", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			requestBody := map[string]interface{}{
				"data": map[string]interface{}{
					"type": "entitlements",
					"attributes": map[string]interface{}{
						"name": fmt.Sprintf("Benchmark Entitlement %d", i),
						"code": fmt.Sprintf("BENCH_%d", i),
					},
				},
			}

			body, _ := json.Marshal(requestBody)
			req := httptest.NewRequest("POST", "/entitlements", bytes.NewBuffer(body))
			req.Header.Set("Content-Type", "application/json")

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(b, http.StatusCreated, w.Code)
		}
	})

	// Cleanup
	db.Exec("DELETE FROM entitlements WHERE account_id = ?", testAccount.ID)
	db.Exec("DELETE FROM accounts WHERE id = ?", testAccount.ID)
}