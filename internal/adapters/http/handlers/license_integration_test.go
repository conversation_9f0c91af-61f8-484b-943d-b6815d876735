package handlers

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/rs/zerolog"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/services"
)

// TestLicenseHandlerSimple tests license handler using existing structures
// Much simpler approach: test the request/response structures and HTTP workflow
func TestLicenseHandlerSimple(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("handler_creation_with_nil_service", func(t *testing.T) {
		// Test handler can be created (structure testing)
		handler := NewLicenseHandler(nil)
		assert.NotNil(t, handler)
	})

	t.Run("using_existing_request_structures", func(t *testing.T) {
		// Test actual ValidateLicenseRequest structure from codebase
		request := ValidateLicenseRequest{
			LicenseKey:         "LIC-12345-ABCDE-67890-FGHIJ",
			MachineFingerprint: stringPtr("fp-mac-12345678"),
			Environment:        stringPtr("production"),
			MachineInfo: map[string]interface{}{
				"hostname":  "production-server",
				"os":        "ubuntu-20.04",
				"cpu_cores": 8,
			},
		}

		// Test JSON marshaling (real workflow)
		jsonBytes, err := json.Marshal(request)
		require.NoError(t, err)

		// Test JSON unmarshaling (real workflow)
		var parsed ValidateLicenseRequest
		err = json.Unmarshal(jsonBytes, &parsed)
		require.NoError(t, err)

		// Verify structures work correctly
		assert.Equal(t, "LIC-12345-ABCDE-67890-FGHIJ", parsed.LicenseKey)
		assert.Equal(t, "fp-mac-12345678", *parsed.MachineFingerprint)
		assert.Equal(t, "production", *parsed.Environment)
		assert.Equal(t, float64(8), parsed.MachineInfo["cpu_cores"])
	})

	t.Run("using_existing_response_structures", func(t *testing.T) {
		// Test actual ValidateLicenseResponse structure from codebase
		response := ValidateLicenseResponse{
			Valid:           true,
			ValidationTime:  time.Now().UTC(),
			ExpiresAt:       timePtr(time.Now().Add(365 * 24 * time.Hour)),
			MachinesUsed:    3,
			MachinesAllowed: 10,
			Claims: map[string]interface{}{
				"feature_premium": true,
				"max_users":       100,
			},
			License: map[string]interface{}{
				"id":   uuid.New().String(),
				"name": "Test License",
			},
		}

		// Test JSON serialization
		jsonBytes, err := json.Marshal(response)
		require.NoError(t, err)

		// Test JSON deserialization
		var parsed ValidateLicenseResponse
		err = json.Unmarshal(jsonBytes, &parsed)
		require.NoError(t, err)

		// Verify response structure
		assert.True(t, parsed.Valid)
		assert.Equal(t, 3, parsed.MachinesUsed)
		assert.Equal(t, 10, parsed.MachinesAllowed)
		assert.NotNil(t, parsed.ExpiresAt)
	})

	t.Run("http_request_workflow_testing", func(t *testing.T) {
		// Test HTTP request creation and processing
		request := ValidateLicenseRequest{
			LicenseKey:         "LIC-WORKFLOW-TEST",
			MachineFingerprint: stringPtr("fp-workflow-test"),
			Environment:        stringPtr("testing"),
		}

		// Create actual HTTP request
		jsonBytes, err := json.Marshal(request)
		require.NoError(t, err)

		req := httptest.NewRequest("POST", "/api/v1/licenses/validate", bytes.NewBuffer(jsonBytes))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer test-token")

		// Test Gin context creation
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// Test Gin JSON binding (part of real workflow)
		var boundRequest ValidateLicenseRequest
		err = c.ShouldBindJSON(&boundRequest)
		require.NoError(t, err)

		// Verify binding worked correctly
		assert.Equal(t, "LIC-WORKFLOW-TEST", boundRequest.LicenseKey)
		assert.Equal(t, "fp-workflow-test", *boundRequest.MachineFingerprint)
		assert.Equal(t, "testing", *boundRequest.Environment)

		t.Log("✅ HTTP workflow components tested successfully")
	})
}

// TestLicenseWorkflowSimple tests the complete workflow simply
func TestLicenseWorkflowSimple(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("test_actual_request_response_flow", func(t *testing.T) {
		// This approach is much simpler:
		// 1. Create real request structures (not manual)
		// 2. Use existing handler methods
		// 3. Test actual HTTP workflow
		// 4. Mock only what's necessary

		// Step 1: Create real ValidateLicenseRequest
		licenseRequest := ValidateLicenseRequest{
			LicenseKey:         "LIC-REAL-12345-ABCDE-67890",
			MachineFingerprint: stringPtr("fp-real-machine"),
			Environment:        stringPtr("production"),
			MachineInfo: map[string]interface{}{
				"hostname":  "production-server-01",
				"os":        "ubuntu-20.04",
				"cpu_cores": 8,
				"memory_gb": 32,
			},
		}

		// Step 2: Test JSON marshaling/unmarshaling (real workflow)
		jsonBytes, err := json.Marshal(licenseRequest)
		require.NoError(t, err)

		var parsedRequest ValidateLicenseRequest
		err = json.Unmarshal(jsonBytes, &parsedRequest)
		require.NoError(t, err)

		// Verify request parsing works correctly
		assert.Equal(t, "LIC-REAL-12345-ABCDE-67890", parsedRequest.LicenseKey)
		assert.Equal(t, "fp-real-machine", *parsedRequest.MachineFingerprint)
		assert.Equal(t, "production", *parsedRequest.Environment)
		assert.Equal(t, "production-server-01", parsedRequest.MachineInfo["hostname"])

		// Step 3: Test HTTP request creation (real workflow)
		req := httptest.NewRequest("POST", "/api/v1/licenses/validate", bytes.NewBuffer(jsonBytes))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer test-token")

		assert.Equal(t, "POST", req.Method)
		assert.Equal(t, "/api/v1/licenses/validate", req.URL.Path)
		assert.Equal(t, "application/json", req.Header.Get("Content-Type"))

		// Step 4: Test Gin binding (real workflow)
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		var boundRequest ValidateLicenseRequest
		err = c.ShouldBindJSON(&boundRequest)
		require.NoError(t, err)

		// Verify Gin binding works
		assert.Equal(t, licenseRequest.LicenseKey, boundRequest.LicenseKey)
		assert.Equal(t, *licenseRequest.MachineFingerprint, *boundRequest.MachineFingerprint)

		t.Log("✅ Complete request-response workflow tested successfully")
	})

	t.Run("test_response_structure", func(t *testing.T) {
		// Test actual ValidateLicenseResponse structure
		response := ValidateLicenseResponse{
			Valid:           true,
			ValidationTime:  time.Now().UTC(),
			ExpiresAt:       timePtr(time.Now().Add(365 * 24 * time.Hour)),
			MachinesUsed:    3,
			MachinesAllowed: 10,
			Claims: map[string]interface{}{
				"feature_premium":      true,
				"feature_basic":        true,
				"max_concurrent_users": 100,
			},
			License: map[string]interface{}{
				"id":   uuid.New().String(),
				"name": "Enterprise License",
			},
			Policy: map[string]interface{}{
				"id":           uuid.New().String(),
				"name":         "Enterprise Policy",
				"max_machines": 10,
			},
		}

		// Test JSON serialization
		jsonBytes, err := json.Marshal(response)
		require.NoError(t, err)

		// Test JSON deserialization
		var parsedResponse ValidateLicenseResponse
		err = json.Unmarshal(jsonBytes, &parsedResponse)
		require.NoError(t, err)

		// Verify response structure
		assert.True(t, parsedResponse.Valid)
		assert.Equal(t, 3, parsedResponse.MachinesUsed)
		assert.Equal(t, 10, parsedResponse.MachinesAllowed)
		assert.True(t, parsedResponse.Claims["feature_premium"].(bool))
		assert.NotNil(t, parsedResponse.ExpiresAt)

		t.Log("✅ Response structure tested successfully")
	})
}

// Helper functions for testing (reuse from license_test.go)
func timePtr(t time.Time) *time.Time {
	return &t
}

// TestLicenseHandlerProductionIntegration tests với real ServiceCoordinator như production
func TestLicenseHandlerProductionIntegration(t *testing.T) {
	// Skip if no database available
	if testing.Short() {
		t.Skip("Skipping production integration test in short mode")
	}

	gin.SetMode(gin.TestMode)
	ctx := context.Background()

	t.Run("complete_license_creation_workflow", func(t *testing.T) {
		// Setup real database with migrations
		db, err := setupTestDatabaseWithMigrations(t)
		if err != nil {
			t.Skipf("Skipping test due to database setup error: %v", err)
			return
		}

		// Create real ServiceCoordinator như production
		serviceCoordinator, err := createTestServiceCoordinator(t)
		if err != nil {
			t.Fatalf("Failed to create service coordinator: %v", err)
		}

		// Cleanup test data after test
		defer cleanupTestData(t, db)

		// Create complete workflow test data with user
		testData, err := setupCompleteWorkflowTestData(ctx, serviceCoordinator)
		if err != nil {
			t.Fatalf("Failed to setup complete test data: %v", err)
		}

		// Create real handler với real ServiceCoordinator
		handler := NewLicenseHandler(serviceCoordinator)

		// Test 1: License validation with user association
		request := ValidateLicenseRequest{
			LicenseKey:         testData.License.Key,
			MachineFingerprint: stringPtr("workflow-test-machine-fp"),
			Environment:        stringPtr("test"),
			MachineInfo: map[string]interface{}{
				"hostname":  "workflow-test-server",
				"os":        "linux",
				"memory":    "16GB",
				"cpu_cores": 8,
				"user_id":   testData.User.ID, // Associate with user
			},
		}

		// Create real HTTP request
		jsonBytes, err := json.Marshal(request)
		require.NoError(t, err)

		req := httptest.NewRequest("POST", "/api/v1/licenses/validate", bytes.NewBuffer(jsonBytes))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer test-token")

		// Create Gin context và set account
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Request = req
		c.Set("account_id", uuid.MustParse(testData.Account.ID))

		// Call REAL handler method - đây là integration test thực sự!
		handler.ValidatePostHandler(c)

		// Verify response từ real workflow
		require.Equal(t, 200, w.Code, "Expected successful validation")

		var response ValidateLicenseResponse
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		// Verify complete response data
		assert.True(t, response.Valid, "License should be valid")
		assert.NotZero(t, response.ValidationTime, "Should have validation time")
		assert.NotNil(t, response.License, "Should return license data")
		assert.NotNil(t, response.Policy, "Should return policy data")
		assert.NotNil(t, response.Account, "Should return account data")

		// Verify machine was registered
		accountUUID, _ := uuid.Parse(testData.Account.ID)
		machine, err := serviceCoordinator.Repositories.Machine().GetByFingerprint(ctx, "workflow-test-machine-fp", accountUUID)
		if err == nil {
			assert.Equal(t, "workflow-test-machine-fp", machine.Fingerprint)
			assert.Equal(t, testData.Account.ID, machine.AccountID)
			assert.Equal(t, testData.License.ID, machine.LicenseID)
			t.Logf("✅ Machine registered successfully: %s", machine.ID)
		}

		t.Logf("✅ Complete license creation workflow test passed")
	})

	t.Run("license_creation_via_handler", func(t *testing.T) {
		// Test creating license through handler (not just validation)
		db, err := setupTestDatabaseWithMigrations(t)
		if err != nil {
			t.Skipf("Skipping test due to database setup error: %v", err)
			return
		}

		serviceCoordinator, err := createTestServiceCoordinator(t)
		if err != nil {
			t.Fatalf("Failed to create service coordinator: %v", err)
		}

		defer cleanupTestData(t, db)

		// Setup base entities (account, product, policy, user)
		testData, err := setupCompleteWorkflowTestData(ctx, serviceCoordinator)
		if err != nil {
			t.Fatalf("Failed to setup test data: %v", err)
		}

		handler := NewLicenseHandler(serviceCoordinator)

		// Test license creation request
		createRequest := struct {
			Data struct {
				Type       string `json:"type"`
				Attributes struct {
					Name     string                 `json:"name"`
					Key      string                 `json:"key,omitempty"`
					Metadata map[string]interface{} `json:"metadata,omitempty"`
				} `json:"attributes"`
				Relationships struct {
					Policy *struct {
						Data struct {
							ID string `json:"id"`
						} `json:"data"`
					} `json:"policy,omitempty"`
					User *struct {
						Data struct {
							ID string `json:"id"`
						} `json:"data"`
					} `json:"user,omitempty"`
				} `json:"relationships"`
			} `json:"data"`
		}{
			Data: struct {
				Type       string `json:"type"`
				Attributes struct {
					Name     string                 `json:"name"`
					Key      string                 `json:"key,omitempty"`
					Metadata map[string]interface{} `json:"metadata,omitempty"`
				} `json:"attributes"`
				Relationships struct {
					Policy *struct {
						Data struct {
							ID string `json:"id"`
						} `json:"data"`
					} `json:"policy,omitempty"`
					User *struct {
						Data struct {
							ID string `json:"id"`
						} `json:"data"`
					} `json:"user,omitempty"`
				} `json:"relationships"`
			}{
				Type: "licenses",
				Attributes: struct {
					Name     string                 `json:"name"`
					Key      string                 `json:"key,omitempty"`
					Metadata map[string]interface{} `json:"metadata,omitempty"`
				}{
					Name: "Handler Created License",
					Metadata: map[string]interface{}{
						"created_via": "handler_test",
					},
				},
				Relationships: struct {
					Policy *struct {
						Data struct {
							ID string `json:"id"`
						} `json:"data"`
					} `json:"policy,omitempty"`
					User *struct {
						Data struct {
							ID string `json:"id"`
						} `json:"data"`
					} `json:"user,omitempty"`
				}{
					Policy: &struct {
						Data struct {
							ID string `json:"id"`
						} `json:"data"`
					}{
						Data: struct {
							ID string `json:"id"`
						}{
							ID: testData.Policy.ID,
						},
					},
					User: &struct {
						Data struct {
							ID string `json:"id"`
						} `json:"data"`
					}{
						Data: struct {
							ID string `json:"id"`
						}{
							ID: testData.User.ID,
						},
					},
				},
			},
		}

		// Create HTTP request for license creation
		jsonBytes, err := json.Marshal(createRequest)
		require.NoError(t, err)

		req := httptest.NewRequest("POST", "/api/v1/licenses", bytes.NewBuffer(jsonBytes))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer test-token")

		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Request = req
		c.Set("account_id", uuid.MustParse(testData.Account.ID))

		// Call license creation handler
		handler.CreateLicenseHandler(c)

		// Verify license was created
		require.Equal(t, 201, w.Code, "Expected license creation success")

		var response map[string]interface{}
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		// Verify response structure
		assert.NotNil(t, response["data"], "Should return license data")
		licenseData := response["data"].(map[string]interface{})
		assert.Equal(t, "Handler Created License", licenseData["name"])

		t.Logf("✅ License creation via handler test passed")
	})

	t.Run("user_license_association_workflow", func(t *testing.T) {
		// Test complete workflow: create user, create license, associate them, validate
		db, err := setupTestDatabaseWithMigrations(t)
		if err != nil {
			t.Skipf("Skipping test due to database setup error: %v", err)
			return
		}

		serviceCoordinator, err := createTestServiceCoordinator(t)
		if err != nil {
			t.Fatalf("Failed to create service coordinator: %v", err)
		}

		defer cleanupTestData(t, db)

		// Setup complete test data
		testData, err := setupCompleteWorkflowTestData(ctx, serviceCoordinator)
		if err != nil {
			t.Fatalf("Failed to setup test data: %v", err)
		}

		handler := NewLicenseHandler(serviceCoordinator)

		// Test 1: Validate license with user association
		request := ValidateLicenseRequest{
			LicenseKey:         testData.License.Key,
			MachineFingerprint: stringPtr("user-assoc-machine-fp"),
			Environment:        stringPtr("test"),
			MachineInfo: map[string]interface{}{
				"hostname":   "user-associated-server",
				"os":         "linux",
				"user_email": testData.User.Email,
				"user_id":    testData.User.ID,
			},
		}

		jsonBytes, err := json.Marshal(request)
		require.NoError(t, err)

		req := httptest.NewRequest("POST", "/api/v1/licenses/validate", bytes.NewBuffer(jsonBytes))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer test-token")

		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Request = req
		c.Set("account_id", uuid.MustParse(testData.Account.ID))

		// Execute validation
		handler.ValidatePostHandler(c)

		// Verify successful validation
		require.Equal(t, 200, w.Code, "Expected successful validation")

		var response ValidateLicenseResponse
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.True(t, response.Valid, "License should be valid")
		assert.NotNil(t, response.License, "Should return license data")
		assert.NotNil(t, response.Account, "Should return account data")

		// Test 2: Verify user can be retrieved and is associated with license
		userUUID, _ := uuid.Parse(testData.User.ID)
		retrievedUser, err := serviceCoordinator.Repositories.User().GetByID(ctx, userUUID)
		require.NoError(t, err)
		assert.Equal(t, testData.User.Email, retrievedUser.Email)
		assert.Equal(t, testData.Account.ID, retrievedUser.AccountID)

		// Test 3: Verify license is associated with user
		licenseUUID, _ := uuid.Parse(testData.License.ID)
		retrievedLicense, err := serviceCoordinator.Repositories.License().GetByID(ctx, licenseUUID)
		require.NoError(t, err)
		assert.NotNil(t, retrievedLicense.UserID)
		assert.Equal(t, testData.User.ID, *retrievedLicense.UserID)

		t.Logf("✅ User-license association workflow test passed")
	})

	t.Run("policy_enforcement_workflow", func(t *testing.T) {
		// Test policy limits enforcement during license validation
		db, err := setupTestDatabaseWithMigrations(t)
		if err != nil {
			t.Skipf("Skipping test due to database setup error: %v", err)
			return
		}

		serviceCoordinator, err := createTestServiceCoordinator(t)
		if err != nil {
			t.Fatalf("Failed to create service coordinator: %v", err)
		}

		defer cleanupTestData(t, db)

		// Setup test data with restrictive policy
		testData, err := setupCompleteWorkflowTestData(ctx, serviceCoordinator)
		if err != nil {
			t.Fatalf("Failed to setup test data: %v", err)
		}

		handler := NewLicenseHandler(serviceCoordinator)

		// Test multiple machine registrations to test policy limits
		machineFingerprints := []string{
			"policy-test-machine-1",
			"policy-test-machine-2",
			"policy-test-machine-3",
			"policy-test-machine-4",
			"policy-test-machine-5",
			"policy-test-machine-6", // This should exceed the limit (MaxMachines: 5)
		}

		successfulValidations := 0
		for i, fingerprint := range machineFingerprints {
			request := ValidateLicenseRequest{
				LicenseKey:         testData.License.Key,
				MachineFingerprint: stringPtr(fingerprint),
				Environment:        stringPtr("test"),
				MachineInfo: map[string]interface{}{
					"hostname": fmt.Sprintf("policy-test-server-%d", i+1),
					"os":       "linux",
				},
			}

			jsonBytes, err := json.Marshal(request)
			require.NoError(t, err)

			req := httptest.NewRequest("POST", "/api/v1/licenses/validate", bytes.NewBuffer(jsonBytes))
			req.Header.Set("Content-Type", "application/json")

			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Request = req
			c.Set("account_id", uuid.MustParse(testData.Account.ID))

			handler.ValidatePostHandler(c)

			var response ValidateLicenseResponse
			json.Unmarshal(w.Body.Bytes(), &response)

			if response.Valid {
				successfulValidations++
			}

			t.Logf("Machine %d (%s): Valid=%v, MachinesUsed=%d, MachinesAllowed=%d",
				i+1, fingerprint, response.Valid, response.MachinesUsed, response.MachinesAllowed)
		}

		// Verify policy enforcement
		assert.LessOrEqual(t, successfulValidations, 5, "Should not exceed policy machine limit")
		t.Logf("✅ Policy enforcement workflow test passed - %d successful validations", successfulValidations)
	})

	t.Run("real_machine_registration_integration", func(t *testing.T) {
		// Setup
		db, err := setupTestDatabase(t)
		if err != nil {
			t.Skipf("Skipping test: %v", err)
			return
		}
		defer cleanupTestDatabase(db)

		serviceCoordinator, err := createProductionServiceCoordinator(db)
		require.NoError(t, err)

		testData, err := setupProductionTestData(ctx, serviceCoordinator)
		require.NoError(t, err)
		defer cleanupProductionTestData(ctx, serviceCoordinator, testData)

		handler := NewLicenseHandler(serviceCoordinator)

		// Test machine registration through license validation
		request := ValidateLicenseRequest{
			LicenseKey:         testData.License.Key,
			MachineFingerprint: stringPtr("new-machine-fingerprint-12345"),
			Environment:        stringPtr("production"),
			MachineInfo: map[string]interface{}{
				"hostname":   "prod-server-01",
				"os":         "ubuntu-22.04",
				"cpu_cores":  16,
				"memory_gb":  64,
				"ip_address": "*************",
			},
		}

		jsonBytes, err := json.Marshal(request)
		require.NoError(t, err)

		req := httptest.NewRequest("POST", "/api/v1/licenses/validate", bytes.NewBuffer(jsonBytes))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Request = req
		c.Set("account_id", uuid.MustParse(testData.Account.ID))

		// Execute real workflow
		handler.ValidatePostHandler(c)

		// Verify machine was registered in database
		accountUUID, _ := uuid.Parse(testData.Account.ID)
		machine, err := serviceCoordinator.Repositories.Machine().GetByFingerprint(ctx, "new-machine-fingerprint-12345", accountUUID)

		if err == nil {
			assert.Equal(t, "new-machine-fingerprint-12345", machine.Fingerprint)
			assert.Equal(t, testData.Account.ID, machine.AccountID)
			t.Logf("✅ Machine registration integration test passed")
		} else {
			t.Logf("Machine not registered (may be expected based on policy): %v", err)
		}
	})

	t.Run("real_cache_integration", func(t *testing.T) {
		// Test real cache workflow
		db, err := setupTestDatabase(t)
		if err != nil {
			t.Skipf("Skipping test: %v", err)
			return
		}
		defer cleanupTestDatabase(db)

		serviceCoordinator, err := createProductionServiceCoordinator(db)
		require.NoError(t, err)

		testData, err := setupProductionTestData(ctx, serviceCoordinator)
		require.NoError(t, err)
		defer cleanupProductionTestData(ctx, serviceCoordinator, testData)

		handler := NewLicenseHandler(serviceCoordinator)

		// First validation - should hit database
		request := ValidateLicenseRequest{
			LicenseKey:         testData.License.Key,
			MachineFingerprint: stringPtr("cache-test-machine"),
			Environment:        stringPtr("test"),
		}

		// Execute first request
		jsonBytes, _ := json.Marshal(request)
		req1 := httptest.NewRequest("POST", "/api/v1/licenses/validate", bytes.NewBuffer(jsonBytes))
		req1.Header.Set("Content-Type", "application/json")
		w1 := httptest.NewRecorder()
		c1, _ := gin.CreateTestContext(w1)
		c1.Request = req1
		c1.Set("account_id", uuid.MustParse(testData.Account.ID))

		start1 := time.Now()
		handler.ValidatePostHandler(c1)
		duration1 := time.Since(start1)

		// Second validation - should hit cache (faster)
		req2 := httptest.NewRequest("POST", "/api/v1/licenses/validate", bytes.NewBuffer(jsonBytes))
		req2.Header.Set("Content-Type", "application/json")
		w2 := httptest.NewRecorder()
		c2, _ := gin.CreateTestContext(w2)
		c2.Request = req2
		c2.Set("account_id", uuid.MustParse(testData.Account.ID))

		start2 := time.Now()
		handler.ValidatePostHandler(c2)
		duration2 := time.Since(start2)

		// Both should succeed
		assert.Equal(t, 200, w1.Code)
		assert.Equal(t, 200, w2.Code)

		t.Logf("✅ Cache integration test - First: %v, Second: %v", duration1, duration2)
	})
}

// Legacy functions - now using testing_utils.go instead

func setupTestDatabase(t *testing.T) (*gorm.DB, error) {
	// Use same credentials as other tests
	dsn := "postgres://gokeys:gokeys_dev_password@localhost:5432/gokeys_test?sslmode=disable"

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	if err != nil {
		return nil, err
	}

	// Create tables manually để tránh GORM relationship issues
	err = db.Exec(`
		CREATE TABLE IF NOT EXISTS accounts (
			id VARCHAR(36) PRIMARY KEY DEFAULT gen_random_uuid(),
			name VARCHAR(255) NOT NULL,
			slug VARCHAR(255) UNIQUE NOT NULL,
			email VARCHAR(255) UNIQUE NOT NULL,
			metadata JSONB,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		);
		
		CREATE TABLE IF NOT EXISTS environments (
			id VARCHAR(36) PRIMARY KEY DEFAULT gen_random_uuid(),
			account_id VARCHAR(36) NOT NULL,
			name VARCHAR(255) NOT NULL,
			code VARCHAR(255) NOT NULL,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		);
		
		CREATE TABLE IF NOT EXISTS products (
			id VARCHAR(36) PRIMARY KEY DEFAULT gen_random_uuid(),
			account_id VARCHAR(36) NOT NULL,
			environment_id VARCHAR(36),
			name VARCHAR(255) NOT NULL,
			code VARCHAR(255) NOT NULL,
			key VARCHAR(255) UNIQUE,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		);
		
		CREATE TABLE IF NOT EXISTS policies (
			id VARCHAR(36) PRIMARY KEY DEFAULT gen_random_uuid(),
			account_id VARCHAR(36) NOT NULL,
			product_id VARCHAR(36) NOT NULL,
			environment_id VARCHAR(36),
			name VARCHAR(255) NOT NULL,
			duration INTEGER,
			strict BOOLEAN DEFAULT false,
			require_heartbeat BOOLEAN DEFAULT false,
			max_machines INTEGER,
			max_users INTEGER,
			scheme VARCHAR(50) DEFAULT 'ED25519_SIGN',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		);
		
		CREATE TABLE IF NOT EXISTS licenses (
			id VARCHAR(36) PRIMARY KEY DEFAULT gen_random_uuid(),
			account_id VARCHAR(36) NOT NULL,
			product_id VARCHAR(36) NOT NULL,
			policy_id VARCHAR(36) NOT NULL,
			name VARCHAR(255) NOT NULL,
			key VARCHAR(255) NOT NULL,
			status VARCHAR(50) DEFAULT 'active',
			expires_at TIMESTAMP,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		);
		
		CREATE TABLE IF NOT EXISTS machines (
			id VARCHAR(36) PRIMARY KEY DEFAULT gen_random_uuid(),
			account_id VARCHAR(36) NOT NULL,
			fingerprint VARCHAR(255) NOT NULL,
			name VARCHAR(255),
			hostname VARCHAR(255),
			platform VARCHAR(255),
			last_seen TIMESTAMP,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		);
	`).Error
	if err != nil {
		return nil, err
	}

	return db, nil
}

func createProductionServiceCoordinator(db *gorm.DB) (*services.ServiceCoordinator, error) {
	// Create real ServiceCoordinator với real dependencies
	logger := zerolog.Nop() // Use no-op logger for tests

	serviceCoordinator := services.NewServiceCoordinator(db, logger)
	return serviceCoordinator, nil
}

func setupProductionTestData(ctx context.Context, sc *services.ServiceCoordinator) (*ProductionTestData, error) {
	// Create test account
	account := &entities.Account{
		Name:  "Integration Test Account",
		Slug:  "integration-test-" + uuid.New().String()[0:8],
		Email: "<EMAIL>",
		Metadata: map[string]interface{}{
			"test_mode": true,
		},
	}
	err := sc.Repositories.Account().Create(ctx, account)
	if err != nil {
		return nil, err
	}

	// Create test environment
	environment := &entities.Environment{
		AccountID: account.ID,
		Name:      "Test Environment",
		Code:      "test",
	}
	err = sc.Repositories.Environment().Create(ctx, environment)
	if err != nil {
		return nil, err
	}

	// Create test product
	product := &entities.Product{
		AccountID:     account.ID,
		EnvironmentID: &environment.ID,
		Name:          "Integration Test Product",
		Code:          "test-product-" + uuid.New().String()[0:8],
	}
	err = sc.Repositories.Product().Create(ctx, product)
	if err != nil {
		return nil, err
	}

	// Create test policy
	policy := &entities.Policy{
		AccountID:        account.ID,
		ProductID:        product.ID,
		EnvironmentID:    &environment.ID,
		Name:             "Integration Test Policy",
		Duration:         intPtr(3600), // 1 hour
		Strict:           false,
		RequireHeartbeat: false,
		MaxMachines:      intPtr(10),
		MaxUsers:         intPtr(5),
		Scheme:           entities.Ed25519Sign,
	}
	err = sc.Repositories.Policy().Create(ctx, policy)
	if err != nil {
		return nil, err
	}

	// Create test license
	license := &entities.License{
		AccountID: account.ID,
		ProductID: product.ID,
		PolicyID:  policy.ID,
		Name:      "Integration Test License",
		Key:       "INTEG-TEST-" + uuid.New().String()[0:8] + "-" + uuid.New().String()[0:8],
		Status:    entities.LicenseStatusActive,
		ExpiresAt: timePtr(time.Now().Add(24 * time.Hour)), // Expires in 24 hours
	}
	err = sc.Repositories.License().Create(ctx, license)
	if err != nil {
		return nil, err
	}

	return &ProductionTestData{
		Account:     account,
		Product:     product,
		Policy:      policy,
		License:     license,
		Environment: environment,
	}, nil
}

func cleanupProductionTestData(ctx context.Context, sc *services.ServiceCoordinator, data *ProductionTestData) {
	// Clean up in reverse order
	if data.License != nil {
		licenseUUID, _ := uuid.Parse(data.License.ID)
		sc.Repositories.License().Delete(ctx, licenseUUID)
	}
	if data.Policy != nil {
		policyUUID, _ := uuid.Parse(data.Policy.ID)
		sc.Repositories.Policy().Delete(ctx, policyUUID)
	}
	if data.Product != nil {
		productUUID, _ := uuid.Parse(data.Product.ID)
		sc.Repositories.Product().Delete(ctx, productUUID)
	}
	if data.Environment != nil {
		envUUID, _ := uuid.Parse(data.Environment.ID)
		sc.Repositories.Environment().Delete(ctx, envUUID)
	}
	if data.Account != nil {
		accountUUID, _ := uuid.Parse(data.Account.ID)
		sc.Repositories.Account().Delete(ctx, accountUUID)
	}
}

func cleanupTestDatabase(db *gorm.DB) {
	sqlDB, _ := db.DB()
	sqlDB.Close()
}

// CompleteWorkflowTestData holds complete test data including user
type CompleteWorkflowTestData struct {
	Account     *entities.Account
	Product     *entities.Product
	Policy      *entities.Policy
	License     *entities.License
	Environment *entities.Environment
	User        *entities.User
}

// setupCompleteWorkflowTestData creates complete test data including user for realistic workflow testing
func setupCompleteWorkflowTestData(ctx context.Context, sc *services.ServiceCoordinator) (*CompleteWorkflowTestData, error) {
	// 1. Create Account (required for all entities)
	account := &entities.Account{
		Name:  "Complete Workflow Test Account",
		Slug:  "complete-workflow-" + uuid.New().String()[0:8],
		Email: "<EMAIL>",
		Metadata: entities.Metadata{
			"test_mode": true,
			"workflow":  "complete_integration_test",
		},
	}
	if err := sc.Repositories.Account().Create(ctx, account); err != nil {
		return nil, err
	}

	// 2. Create Environment (optional but realistic)
	environment := &entities.Environment{
		AccountID: account.ID,
		Name:      "Complete Workflow Environment",
		Code:      "complete-test",
	}
	if err := sc.Repositories.Environment().Create(ctx, environment); err != nil {
		return nil, err
	}

	// 3. Create User (required for license association)
	user := &entities.User{
		AccountID:     account.ID,
		EnvironmentID: &environment.ID,
		Email:         "<EMAIL>",
		FirstName:     "Test",
		LastName:      "User",
		Password:      "hashed_password_here", // In real scenario, this would be properly hashed
		Role:          entities.UserRoleUser,
		Status:        entities.UserStatusActive,
		Metadata: entities.Metadata{
			"created_by": "integration_test",
		},
	}
	if err := sc.Repositories.User().Create(ctx, user); err != nil {
		return nil, err
	}

	// 4. Create Product (required for License)
	product := &entities.Product{
		AccountID:     account.ID,
		EnvironmentID: &environment.ID,
		Name:          "Complete Workflow Product",
		Code:          "complete-product-" + uuid.New().String()[0:8],
		Description:   "Product for complete workflow testing",
		Metadata: entities.Metadata{
			"test_product": true,
		},
	}
	if err := sc.Repositories.Product().Create(ctx, product); err != nil {
		return nil, err
	}

	// 5. Create Policy (required for License)
	policy := &entities.Policy{
		AccountID:        account.ID,
		ProductID:        product.ID,
		EnvironmentID:    &environment.ID,
		Name:             "Complete Workflow Policy",
		Description:      "Policy for complete workflow testing",
		Duration:         intPtr(7200), // 2 hours
		Strict:           false,
		RequireHeartbeat: false,
		MaxMachines:      intPtr(5),
		MaxUsers:         intPtr(3),
		MaxCores:         intPtr(16),
		Scheme:           entities.Ed25519Sign,
		Metadata: entities.Metadata{
			"test_policy": true,
		},
	}
	if err := sc.Repositories.Policy().Create(ctx, policy); err != nil {
		return nil, err
	}

	// 6. Create License (requires Account, Product, Policy, and optionally User)
	license := &entities.License{
		AccountID:     account.ID,
		ProductID:     product.ID,
		PolicyID:      policy.ID,
		EnvironmentID: &environment.ID,
		UserID:        &user.ID, // Associate license with user
		Key:           "COMPLETE-WORKFLOW-" + uuid.New().String()[0:8] + "-" + uuid.New().String()[0:8],
		Name:          "Complete Workflow License",
		Status:        entities.LicenseStatusActive,
		ExpiresAt:     timePtr(time.Now().Add(48 * time.Hour)), // Expires in 48 hours
		Metadata: entities.Metadata{
			"test_license": true,
			"workflow":     "complete",
		},
	}
	if err := sc.Repositories.License().Create(ctx, license); err != nil {
		return nil, err
	}

	return &CompleteWorkflowTestData{
		Account:     account,
		Product:     product,
		Policy:      policy,
		License:     license,
		Environment: environment,
		User:        user,
	}, nil
}
