package handlers

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/services"
)

// Integration Test Server Setup
type IntegrationTestServer struct {
	server             *httptest.Server
	serviceCoordinator *services.ServiceCoordinator
	router             *gin.Engine
}

// NewIntegrationTestServer creates a new test server with real HTTP endpoints
func NewIntegrationTestServer(sc *services.ServiceCoordinator) *IntegrationTestServer {
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Setup routes similar to main server
	api := router.Group("/api")
	v1 := api.Group("/v1")

	// Initialize handlers
	accountHandler := NewAccountHandler(sc)
	productHandler := NewProductHandler(sc)
	policyHandler := NewPolicyHandler(sc)
	licenseHandler := NewLicenseHandler(sc)

	// Setup basic routes for testing (no auth middleware for simplicity)
	v1.POST("/accounts", accountHandler.CreateAccount)
	v1.GET("/accounts/:id", accountHandler.GetAccount)

	v1.POST("/products", productHandler.CreateProduct)
	v1.GET("/products/:id", productHandler.GetProduct)

	v1.POST("/policies", policyHandler.CreatePolicy)
	v1.GET("/policies/:id", policyHandler.GetPolicy)

	v1.POST("/licenses", licenseHandler.CreateLicense)
	v1.GET("/licenses/:id", licenseHandler.GetLicense)
	v1.POST("/licenses/validate", licenseHandler.ValidatePostHandler)

	server := httptest.NewServer(router)

	return &IntegrationTestServer{
		server:             server,
		serviceCoordinator: sc,
		router:             router,
	}
}

func (its *IntegrationTestServer) Close() {
	its.server.Close()
}

func (its *IntegrationTestServer) URL() string {
	return its.server.URL
}

// Helper function to make HTTP requests
func (its *IntegrationTestServer) makeRequest(method, path string, body interface{}) (*http.Response, error) {
	var reqBody []byte
	var err error

	if body != nil {
		reqBody, err = json.Marshal(body)
		if err != nil {
			return nil, err
		}
	}

	req, err := http.NewRequest(method, its.URL()+path, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	return client.Do(req)
}

// Test-only structs for API requests/responses
type TestAccountCreateRequest struct {
	Name  string `json:"name" binding:"required"`
	Slug  string `json:"slug" binding:"required"`
	Email string `json:"email" binding:"required,email"`
}

type TestProductCreateRequest struct {
	Name        string                 `json:"name" binding:"required"`
	Code        string                 `json:"code" binding:"required"`
	Description string                 `json:"description,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

type TestProductResponse struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Code        string                 `json:"code"`
	Description string                 `json:"description,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	Created     string                 `json:"created_at"`
	Updated     string                 `json:"updated_at"`
}

type TestPolicyCreateRequest struct {
	Name             string                 `json:"name" binding:"required"`
	Description      string                 `json:"description,omitempty"`
	ProductID        string                 `json:"product_id" binding:"required"`
	Duration         *int                   `json:"duration,omitempty"`
	MaxMachines      *int                   `json:"max_machines,omitempty"`
	MaxUsers         *int                   `json:"max_users,omitempty"`
	MaxCores         *int                   `json:"max_cores,omitempty"`
	RequireHeartbeat bool                   `json:"require_heartbeat"`
	Strict           bool                   `json:"strict"`
	Metadata         map[string]interface{} `json:"metadata,omitempty"`
}

type TestPolicyResponse struct {
	ID               string                 `json:"id"`
	Name             string                 `json:"name"`
	Description      string                 `json:"description,omitempty"`
	ProductID        string                 `json:"product_id"`
	Duration         *int                   `json:"duration,omitempty"`
	MaxMachines      *int                   `json:"max_machines,omitempty"`
	MaxUsers         *int                   `json:"max_users,omitempty"`
	MaxCores         *int                   `json:"max_cores,omitempty"`
	RequireHeartbeat bool                   `json:"require_heartbeat"`
	Strict           bool                   `json:"strict"`
	Metadata         map[string]interface{} `json:"metadata,omitempty"`
	Created          string                 `json:"created_at"`
	Updated          string                 `json:"updated_at"`
}

type UserCreateRequest struct {
	Email     string                 `json:"email" binding:"required,email"`
	FirstName string                 `json:"first_name" binding:"required"`
	LastName  string                 `json:"last_name" binding:"required"`
	Password  string                 `json:"password" binding:"required,min=8"`
	Role      string                 `json:"role" binding:"required"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

type UserResponse struct {
	ID        string                 `json:"id"`
	Email     string                 `json:"email"`
	FirstName string                 `json:"first_name"`
	LastName  string                 `json:"last_name"`
	Role      string                 `json:"role"`
	Status    string                 `json:"status"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
	Created   string                 `json:"created_at"`
	Updated   string                 `json:"updated_at"`
}

// Mock handlers for testing workflow - these would call the real handlers in production
type TestProductHandler struct {
	serviceCoordinator *services.ServiceCoordinator
}

func NewTestProductHandler(sc *services.ServiceCoordinator) *TestProductHandler {
	return &TestProductHandler{serviceCoordinator: sc}
}

func (h *TestProductHandler) CreateProduct(c *gin.Context) {
	// Simplified product creation for testing
	var req TestProductCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"error": "validation_failed", "details": err.Error()})
		return
	}

	accountID, _ := c.Get("account_id")
	product := &entities.Product{
		AccountID:   accountID.(uuid.UUID).String(),
		Name:        req.Name,
		Code:        req.Code,
		Description: req.Description,
		Metadata:    req.Metadata,
	}

	if err := h.serviceCoordinator.Repositories.Product().Create(c.Request.Context(), product); err != nil {
		c.JSON(500, gin.H{"error": "creation_failed", "details": err.Error()})
		return
	}

	response := TestProductResponse{
		ID:          product.ID,
		Name:        product.Name,
		Code:        product.Code,
		Description: product.Description,
		Metadata:    product.Metadata,
		Created:     product.CreatedAt.Format("2006-01-02T15:04:05Z"),
		Updated:     product.UpdatedAt.Format("2006-01-02T15:04:05Z"),
	}

	c.JSON(201, response)
}

type TestPolicyHandler struct {
	serviceCoordinator *services.ServiceCoordinator
}

func NewTestPolicyHandler(sc *services.ServiceCoordinator) *TestPolicyHandler {
	return &TestPolicyHandler{serviceCoordinator: sc}
}

func (h *TestPolicyHandler) CreatePolicy(c *gin.Context) {
	// Simplified policy creation for testing
	var req TestPolicyCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"error": "validation_failed", "details": err.Error()})
		return
	}

	accountID, _ := c.Get("account_id")
	policy := &entities.Policy{
		AccountID:        accountID.(uuid.UUID).String(),
		ProductID:        req.ProductID,
		Name:             req.Name,
		Description:      req.Description,
		Duration:         req.Duration,
		MaxMachines:      req.MaxMachines,
		MaxUsers:         req.MaxUsers,
		MaxCores:         req.MaxCores,
		RequireHeartbeat: req.RequireHeartbeat,
		Strict:           req.Strict,
		Scheme:           entities.Ed25519Sign,
		Metadata:         req.Metadata,
	}

	if err := h.serviceCoordinator.Repositories.Policy().Create(c.Request.Context(), policy); err != nil {
		c.JSON(500, gin.H{"error": "creation_failed", "details": err.Error()})
		return
	}

	response := TestPolicyResponse{
		ID:               policy.ID,
		Name:             policy.Name,
		Description:      policy.Description,
		ProductID:        policy.ProductID,
		Duration:         policy.Duration,
		MaxMachines:      policy.MaxMachines,
		MaxUsers:         policy.MaxUsers,
		MaxCores:         policy.MaxCores,
		RequireHeartbeat: policy.RequireHeartbeat,
		Strict:           policy.Strict,
		Metadata:         policy.Metadata,
		Created:          policy.CreatedAt.Format("2006-01-02T15:04:05Z"),
		Updated:          policy.UpdatedAt.Format("2006-01-02T15:04:05Z"),
	}

	c.JSON(201, response)
}

type TestUserHandler struct {
	serviceCoordinator *services.ServiceCoordinator
}

func NewTestUserHandler(sc *services.ServiceCoordinator) *TestUserHandler {
	return &TestUserHandler{serviceCoordinator: sc}
}

func (h *TestUserHandler) CreateUser(c *gin.Context) {
	// Simplified user creation for testing
	var req UserCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"error": "validation_failed", "details": err.Error()})
		return
	}

	accountID, _ := c.Get("account_id")
	user := &entities.User{
		AccountID: accountID.(uuid.UUID).String(),
		Email:     req.Email,
		FirstName: req.FirstName,
		LastName:  req.LastName,
		Password:  req.Password, // In real implementation, this would be hashed
		Role:      entities.UserRole(req.Role),
		Status:    entities.UserStatusActive,
		Metadata:  req.Metadata,
	}

	if err := h.serviceCoordinator.Repositories.User().Create(c.Request.Context(), user); err != nil {
		c.JSON(500, gin.H{"error": "creation_failed", "details": err.Error()})
		return
	}

	response := UserResponse{
		ID:        user.ID,
		Email:     user.Email,
		FirstName: user.FirstName,
		LastName:  user.LastName,
		Role:      string(user.Role),
		Status:    string(user.Status),
		Metadata:  user.Metadata,
		Created:   user.CreatedAt.Format("2006-01-02T15:04:05Z"),
		Updated:   user.UpdatedAt.Format("2006-01-02T15:04:05Z"),
	}

	c.JSON(201, response)
}

// Helper functions for testing (avoid duplicates with other test files)
func testStringPtr(s string) *string {
	return &s
}

func testIntPtr(i int) *int {
	return &i
}

func testTimePtr(t time.Time) *time.Time {
	return &t
}

// setupRestrictivePolicyTestData creates test data with restrictive policy for testing limits
func setupRestrictivePolicyTestData(ctx context.Context, sc *services.ServiceCoordinator) (*CompleteWorkflowTestData, error) {
	// 1. Create Account
	account := &entities.Account{
		Name:  "Restrictive Policy Test Account",
		Slug:  "restrictive-policy-" + uuid.New().String()[0:8],
		Email: "<EMAIL>",
	}
	if err := sc.Repositories.Account().Create(ctx, account); err != nil {
		return nil, err
	}

	// 2. Create Environment
	environment := &entities.Environment{
		AccountID:         account.ID,
		Name:              "Restrictive Test Environment",
		Code:              "restrictive-test",
		IsolationStrategy: entities.IsolationStrategyShared,
	}
	if err := sc.Repositories.Environment().Create(ctx, environment); err != nil {
		return nil, err
	}

	// 3. Create Product
	product := &entities.Product{
		AccountID:     account.ID,
		EnvironmentID: &environment.ID,
		Name:          "Restrictive Test Product",
		Code:          "restrictive-test-product",
		Description:   "Product for testing restrictive policies",
	}
	if err := sc.Repositories.Product().Create(ctx, product); err != nil {
		return nil, err
	}

	// 4. Create Restrictive Policy (low limits for testing)
	policy := &entities.Policy{
		AccountID:     account.ID,
		ProductID:     product.ID,
		EnvironmentID: &environment.ID,
		Name:          "Restrictive Test Policy",
		Duration:      testIntPtr(365 * 24 * 60 * 60), // 1 year in seconds
		Scheme:        entities.Ed25519Sign,
		MaxMachines:   testIntPtr(3), // Low limit for testing
		MaxUsers:      testIntPtr(2), // Low limit for testing
		MaxUses:       testIntPtr(100),
		Strict:        true,
	}
	if err := sc.Repositories.Policy().Create(ctx, policy); err != nil {
		return nil, err
	}

	// 5. Create User
	user := &entities.User{
		AccountID:     account.ID,
		EnvironmentID: &environment.ID,
		Email:         "<EMAIL>",
		FirstName:     "Restrictive",
		LastName:      "Test User",
		Status:        entities.UserStatusActive,
	}
	if err := sc.Repositories.User().Create(ctx, user); err != nil {
		return nil, err
	}

	// 6. Create License with restrictive policy
	license := &entities.License{
		AccountID:     account.ID,
		ProductID:     product.ID,
		PolicyID:      policy.ID,
		EnvironmentID: &environment.ID,
		UserID:        &user.ID,
		Key:           "RESTRICTIVE-TEST-" + uuid.New().String()[0:8] + "-" + uuid.New().String()[0:8],
		Name:          "Restrictive Test License",
		Status:        entities.LicenseStatusActive,
		ExpiresAt:     testTimePtr(time.Now().Add(48 * time.Hour)),
		Metadata: entities.Metadata{
			"test_license": true,
			"policy_type":  "restrictive",
		},
	}
	if err := sc.Repositories.License().Create(ctx, license); err != nil {
		return nil, err
	}

	return &CompleteWorkflowTestData{
		Account:     account,
		Environment: environment,
		Product:     product,
		Policy:      policy,
		User:        user,
		License:     license,
	}, nil
}

// setupCompleteWorkflowTestData creates complete test data for workflow testing
func setupCompleteWorkflowTestData(ctx context.Context, sc *services.ServiceCoordinator) (*CompleteWorkflowTestData, error) {
	// 1. Create Account
	account := &entities.Account{
		Name:  "Complete Workflow Test Account",
		Slug:  "complete-workflow-" + uuid.New().String()[0:8],
		Email: "<EMAIL>",
		Metadata: entities.Metadata{
			"test_mode": true,
			"workflow":  "complete_integration_test",
		},
	}
	if err := sc.Repositories.Account().Create(ctx, account); err != nil {
		return nil, err
	}

	// 2. Create Environment
	environment := &entities.Environment{
		AccountID: account.ID,
		Name:      "Complete Workflow Environment",
		Code:      "complete-test",
	}
	if err := sc.Repositories.Environment().Create(ctx, environment); err != nil {
		return nil, err
	}

	// 3. Create User
	user := &entities.User{
		AccountID:     account.ID,
		EnvironmentID: &environment.ID,
		Email:         "<EMAIL>",
		FirstName:     "Test",
		LastName:      "User",
		Password:      "hashed_password_here",
		Role:          entities.UserRoleUser,
		Status:        entities.UserStatusActive,
		Metadata: entities.Metadata{
			"created_by": "integration_test",
		},
	}
	if err := sc.Repositories.User().Create(ctx, user); err != nil {
		return nil, err
	}

	// 4. Create Product
	product := &entities.Product{
		AccountID:     account.ID,
		EnvironmentID: &environment.ID,
		Name:          "Complete Workflow Product",
		Code:          "complete-product-" + uuid.New().String()[0:8],
		Description:   "Product for complete workflow testing",
		Metadata: entities.Metadata{
			"test_product": true,
		},
	}
	if err := sc.Repositories.Product().Create(ctx, product); err != nil {
		return nil, err
	}

	// 5. Create Policy
	policy := &entities.Policy{
		AccountID:        account.ID,
		ProductID:        product.ID,
		EnvironmentID:    &environment.ID,
		Name:             "Complete Workflow Policy",
		Description:      "Policy for complete workflow testing",
		Duration:         testIntPtr(7200), // 2 hours
		Strict:           false,
		RequireHeartbeat: false,
		MaxMachines:      testIntPtr(5),
		MaxUsers:         testIntPtr(3),
		MaxCores:         testIntPtr(16),
		Scheme:           entities.Ed25519Sign,
		Metadata: entities.Metadata{
			"test_policy": true,
		},
	}
	if err := sc.Repositories.Policy().Create(ctx, policy); err != nil {
		return nil, err
	}

	// 6. Create License
	license := &entities.License{
		AccountID:     account.ID,
		ProductID:     product.ID,
		PolicyID:      policy.ID,
		EnvironmentID: &environment.ID,
		UserID:        &user.ID,
		Key:           "COMPLETE-WORKFLOW-" + uuid.New().String()[0:8] + "-" + uuid.New().String()[0:8],
		Name:          "Complete Workflow License",
		Status:        entities.LicenseStatusActive,
		ExpiresAt:     testTimePtr(time.Now().Add(48 * time.Hour)),
		Metadata: entities.Metadata{
			"test_license": true,
			"workflow":     "complete",
		},
	}
	if err := sc.Repositories.License().Create(ctx, license); err != nil {
		return nil, err
	}

	return &CompleteWorkflowTestData{
		Account:        account,
		Product:        product,
		Policy:         policy,
		License:        license,
		Environment:    environment,
		User:           user,
		CreatedLicense: nil, // Will be set by tests
	}, nil
}

// CompleteWorkflowTestData holds complete test data including user
type CompleteWorkflowTestData struct {
	Account        *entities.Account
	Product        *entities.Product
	Policy         *entities.Policy
	License        *entities.License
	Environment    *entities.Environment
	User           *entities.User
	CreatedLicense *entities.License // For licenses created during tests
}

// Helper functions for license validation testing
func performLicenseValidation(t *testing.T, handler *LicenseHandler, req ValidateLicenseRequest, accountID string) ValidateLicenseResponse {
	w := performLicenseValidationRaw(t, handler, req, accountID)
	require.Equal(t, 200, w.Code, "License validation should succeed")

	var response ValidateLicenseResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	return response
}

func performLicenseValidationRaw(t *testing.T, handler *LicenseHandler, req ValidateLicenseRequest, accountID string) *httptest.ResponseRecorder {
	jsonBytes, err := json.Marshal(req)
	require.NoError(t, err)

	httpReq := httptest.NewRequest("POST", "/api/v1/licenses/validate", bytes.NewBuffer(jsonBytes))
	httpReq.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httpReq
	c.Set("account_id", uuid.MustParse(accountID))

	handler.ValidatePostHandler(c)

	return w
}

// TestHTTPIntegrationDemo - Demo test showing HTTP-based integration testing
func TestHTTPIntegrationDemo(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("http_server_setup_demo", func(t *testing.T) {
		// Create a mock service coordinator for demo
		sc := &services.ServiceCoordinator{} // This would be properly initialized in real tests

		// Create test server with real HTTP endpoints
		testServer := NewIntegrationTestServer(sc)
		defer testServer.Close()

		// Demo: Test server is running and accessible
		assert.NotEmpty(t, testServer.URL())
		assert.Contains(t, testServer.URL(), "http://127.0.0.1:")

		t.Logf("✅ HTTP Test Server created successfully at: %s", testServer.URL())
		t.Logf("✅ Available endpoints:")
		t.Logf("   POST %s/api/v1/accounts", testServer.URL())
		t.Logf("   POST %s/api/v1/products", testServer.URL())
		t.Logf("   POST %s/api/v1/policies", testServer.URL())
		t.Logf("   POST %s/api/v1/licenses", testServer.URL())
		t.Logf("   POST %s/api/v1/licenses/validate", testServer.URL())

		// Demo: Make a simple HTTP request (will fail without proper setup, but shows structure)
		resp, err := testServer.makeRequest("GET", "/api/v1/accounts/test", nil)
		if err == nil {
			defer resp.Body.Close()
			t.Logf("✅ HTTP request successful, status: %d", resp.StatusCode)
		} else {
			t.Logf("ℹ️  HTTP request failed as expected (no database): %v", err)
		}
	})
}

// TestLicenseHandlerIntegration - Comprehensive integration tests for license handler
func TestLicenseHandlerIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration tests in short mode")
	}

	gin.SetMode(gin.TestMode)
	ctx := context.Background()

	t.Run("complete_api_workflow_via_http", func(t *testing.T) {
		// Test complete workflow via real HTTP requests: Account -> Product -> Policy -> License -> Validate
		db, err := setupTestDatabaseWithMigrations(t)
		if err != nil {
			t.Skipf("Skipping test due to database setup error: %v", err)
			return
		}

		serviceCoordinator, err := createTestServiceCoordinator(t)
		if err != nil {
			t.Fatalf("Failed to create service coordinator: %v", err)
		}

		defer cleanupTestData(t, db)

		// Create test server with real HTTP endpoints
		testServer := NewIntegrationTestServer(serviceCoordinator)
		defer testServer.Close()

		var createdAccount AccountResponse
		var createdProduct ProductResponse
		var createdPolicy PolicyResponse
		var createdLicense LicenseResponse

		// Step 1: Create Account via HTTP API
		t.Run("1_create_account_via_http", func(t *testing.T) {
			createReq := AccountCreateRequest{
				Name:  "API Workflow Test Company",
				Slug:  "api-workflow-" + uuid.New().String()[0:8],
				Email: "<EMAIL>",
				Metadata: map[string]interface{}{
					"plan":        "enterprise",
					"created_via": "api_integration_test",
				},
			}

			resp, err := testServer.makeRequest("POST", "/api/v1/accounts", createReq)
			require.NoError(t, err)
			defer resp.Body.Close()

			require.Equal(t, http.StatusCreated, resp.StatusCode, "Account creation should succeed")

			err = json.NewDecoder(resp.Body).Decode(&createdAccount)
			require.NoError(t, err)

			assert.Equal(t, "API Workflow Test Company", createdAccount.Name)
			assert.NotEmpty(t, createdAccount.ID)

			t.Logf("✅ Step 1: Created account via HTTP: %s (ID: %s)", createdAccount.Name, createdAccount.ID)
		})

		// Step 2: Create Product via HTTP API
		t.Run("2_create_product_via_http", func(t *testing.T) {
			if createdAccount.ID == "" {
				t.Skip("Account not created")
			}

			createReq := ProductCreateRequest{
				Name:        "API Test Software",
				Code:        "api-software-v1",
				Description: "Software product created via API test",
				Metadata: map[string]interface{}{
					"version":     "1.0.0",
					"created_via": "api_test",
				},
			}

			resp, err := testServer.makeRequest("POST", "/api/v1/products", createReq)
			require.NoError(t, err)
			defer resp.Body.Close()

			require.Equal(t, http.StatusCreated, resp.StatusCode, "Product creation should succeed")

			err = json.NewDecoder(resp.Body).Decode(&createdProduct)
			require.NoError(t, err)

			assert.Equal(t, "API Test Software", createdProduct.Name)
			assert.Equal(t, "api-software-v1", createdProduct.Code)

			t.Logf("✅ Step 2: Created product via HTTP: %s (ID: %s)", createdProduct.Name, createdProduct.ID)
		})

		// Step 3: Create Policy via HTTP API
		t.Run("3_create_policy_via_http", func(t *testing.T) {
			if createdProduct.ID == "" {
				t.Skip("Product not created")
			}

			createReq := PolicyCreateRequest{
				Name:             "API Test Policy",
				Description:      "Policy created via API test",
				ProductID:        createdProduct.ID,
				Duration:         intPtr(86400), // 24 hours
				MaxMachines:      intPtr(5),
				MaxUsers:         intPtr(10),
				MaxCores:         intPtr(32),
				RequireHeartbeat: false,
				Strict:           false,
				Metadata: map[string]interface{}{
					"created_via": "api_test",
				},
			}

			resp, err := testServer.makeRequest("POST", "/api/v1/policies", createReq)
			require.NoError(t, err)
			defer resp.Body.Close()

			require.Equal(t, http.StatusCreated, resp.StatusCode, "Policy creation should succeed")

			err = json.NewDecoder(resp.Body).Decode(&createdPolicy)
			require.NoError(t, err)

			assert.Equal(t, "API Test Policy", createdPolicy.Name)
			assert.Equal(t, createdProduct.ID, createdPolicy.ProductID)

			t.Logf("✅ Step 3: Created policy: %s (ID: %s)", createdPolicy.Name, createdPolicy.ID)
		})

		// Step 4: Skip User creation for now (not in test server setup)
		// TODO: Add user creation endpoint to test server if needed

		// Step 4: Create License via HTTP API
		t.Run("4_create_license_via_http", func(t *testing.T) {
			if createdPolicy.ID == "" {
				t.Skip("Policy not created")
			}

			createReq := LicenseCreateRequest{
				Name:     "API Workflow License",
				PolicyID: createdPolicy.ID,
				// UserID is optional for license creation
				Metadata: map[string]interface{}{
					"created_via": "api_workflow_test",
					"step":        4,
				},
			}

			resp, err := testServer.makeRequest("POST", "/api/v1/licenses", createReq)
			require.NoError(t, err)
			defer resp.Body.Close()

			require.Equal(t, http.StatusCreated, resp.StatusCode, "License creation should succeed")

			err = json.NewDecoder(resp.Body).Decode(&createdLicense)
			require.NoError(t, err)

			assert.Equal(t, "API Workflow License", createdLicense.Name)
			assert.NotEmpty(t, createdLicense.Key)
			assert.Equal(t, "ACTIVE", createdLicense.Status)
			assert.Equal(t, createdPolicy.ID, createdLicense.PolicyID)

			t.Logf("✅ Step 4: Created license via HTTP: %s (Key: %s)", createdLicense.Name, createdLicense.Key)
		})

		// Step 5: Validate License via HTTP API
		t.Run("5_validate_license_via_http", func(t *testing.T) {
			if createdLicense.Key == "" {
				t.Skip("License not created")
			}

			validateReq := ValidateLicenseRequest{
				LicenseKey:         createdLicense.Key,
				MachineFingerprint: stringPtr("api-workflow-machine-001"),
				Environment:        stringPtr("production"),
				MachineInfo: map[string]interface{}{
					"hostname":  "api-workflow-server",
					"os":        "linux",
					"memory":    "32GB",
					"cpu_cores": 16,
					"product":   createdProduct.Name,
					"policy":    createdPolicy.Name,
				},
			}

			resp, err := testServer.makeRequest("POST", "/api/v1/licenses/validate", validateReq)
			require.NoError(t, err)
			defer resp.Body.Close()

			require.Equal(t, http.StatusOK, resp.StatusCode, "License validation should succeed")

			var validateResponse ValidateLicenseResponse
			err = json.NewDecoder(resp.Body).Decode(&validateResponse)
			require.NoError(t, err)

			assert.True(t, validateResponse.Valid, "License should be valid")
			assert.NotNil(t, validateResponse.License, "Should return license data")
			assert.NotNil(t, validateResponse.Policy, "Should return policy data")
			assert.NotNil(t, validateResponse.Account, "Should return account data")
			assert.Equal(t, 1, validateResponse.MachinesUsed, "Should have 1 machine registered")
			assert.LessOrEqual(t, validateResponse.MachinesUsed, validateResponse.MachinesAllowed, "Should not exceed machine limit")

			t.Logf("✅ Step 6: Validated license successfully")
			t.Logf("   License: %s", createdLicense.Key)
			t.Logf("   Machine: api-workflow-machine-001")
			t.Logf("   Machines used: %d/%d", validateResponse.MachinesUsed, validateResponse.MachinesAllowed)
			// User step was skipped in this test
			t.Logf("   Product: %s", createdProduct.Name)
			t.Logf("   Policy: %s", createdPolicy.Name)
		})

		t.Logf("✅ Complete API workflow test passed - All 6 steps executed successfully")
		t.Logf("   1. Account: %s", createdAccount.Name)
		t.Logf("   2. Product: %s", createdProduct.Name)
		t.Logf("   3. Policy: %s", createdPolicy.Name)
		// User step was skipped
		t.Logf("   5. License: %s", createdLicense.Key)
		t.Logf("   6. Validation: SUCCESS")
	})

	t.Run("license_validation_scenarios", func(t *testing.T) {
		// Test various license validation scenarios using repository-created data
		db, err := setupTestDatabaseWithMigrations(t)
		if err != nil {
			t.Skipf("Skipping test due to database setup error: %v", err)
			return
		}

		serviceCoordinator, err := createTestServiceCoordinator(t)
		if err != nil {
			t.Fatalf("Failed to create service coordinator: %v", err)
		}

		defer cleanupTestData(t, db)

		// Setup test data via repository (not API)
		testData, err := setupCompleteWorkflowTestData(ctx, serviceCoordinator)
		if err != nil {
			t.Fatalf("Failed to setup test data: %v", err)
		}

		handler := NewLicenseHandler(serviceCoordinator)

		// Test 1: Valid license validation
		t.Run("valid_license_validation", func(t *testing.T) {
			createReq := LicenseCreateRequest{
				Name:     "Integration Test License",
				PolicyID: testData.Policy.ID,
				UserID:   &testData.User.ID,
				Metadata: map[string]interface{}{
					"test_type":  "integration",
					"created_by": "test_suite",
				},
			}

			jsonBytes, err := json.Marshal(createReq)
			require.NoError(t, err)

			req := httptest.NewRequest("POST", "/api/v1/licenses", bytes.NewBuffer(jsonBytes))
			req.Header.Set("Content-Type", "application/json")

			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Request = req
			c.Set("account_id", uuid.MustParse(testData.Account.ID))

			// Call handler
			handler.CreateLicense(c)

			// Verify response
			require.Equal(t, 201, w.Code, "Expected license creation success")

			var response LicenseResponse
			err = json.Unmarshal(w.Body.Bytes(), &response)
			require.NoError(t, err)

			assert.Equal(t, "Integration Test License", response.Name)
			assert.NotEmpty(t, response.Key)
			assert.Equal(t, "ACTIVE", response.Status)
			assert.Equal(t, testData.Policy.ID, response.PolicyID)
			assert.Equal(t, testData.User.ID, *response.UserID)

			t.Logf("✅ Created license: %s (Key: %s)", response.Name, response.Key)

			// Store for next tests
			testData.CreatedLicense = &entities.License{
				ID:       response.ID,
				Name:     response.Name,
				Key:      response.Key,
				Status:   entities.LicenseStatus(response.Status),
				PolicyID: response.PolicyID,
				UserID:   response.UserID,
			}
		})

		// Test 2: Get License via Handler
		t.Run("get_license", func(t *testing.T) {
			if testData.CreatedLicense == nil {
				t.Skip("No license created in previous test")
			}

			req := httptest.NewRequest("GET", "/api/v1/licenses/"+testData.CreatedLicense.ID, nil)
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Request = req
			c.Params = []gin.Param{{Key: "id", Value: testData.CreatedLicense.ID}}
			c.Set("account_id", uuid.MustParse(testData.Account.ID))

			handler.GetLicense(c)

			require.Equal(t, 200, w.Code, "Expected license retrieval success")

			var response LicenseResponse
			err := json.Unmarshal(w.Body.Bytes(), &response)
			require.NoError(t, err)

			assert.Equal(t, testData.CreatedLicense.Name, response.Name)
			assert.Equal(t, testData.CreatedLicense.Key, response.Key)
			assert.Equal(t, string(testData.CreatedLicense.Status), response.Status)

			t.Logf("✅ Retrieved license: %s", response.Name)
		})

		// Test 3: Update License via Handler
		t.Run("update_license", func(t *testing.T) {
			if testData.CreatedLicense == nil {
				t.Skip("No license created in previous test")
			}

			newName := "Updated Integration Test License"
			updateReq := LicenseUpdateRequest{
				Name: &newName,
				Metadata: map[string]interface{}{
					"updated":    true,
					"updated_at": time.Now().Format(time.RFC3339),
				},
			}

			jsonBytes, err := json.Marshal(updateReq)
			require.NoError(t, err)

			req := httptest.NewRequest("PUT", "/api/v1/licenses/"+testData.CreatedLicense.ID, bytes.NewBuffer(jsonBytes))
			req.Header.Set("Content-Type", "application/json")

			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Request = req
			c.Params = []gin.Param{{Key: "id", Value: testData.CreatedLicense.ID}}
			c.Set("account_id", uuid.MustParse(testData.Account.ID))

			handler.UpdateLicense(c)

			require.Equal(t, 200, w.Code, "Expected license update success")

			var response LicenseResponse
			err = json.Unmarshal(w.Body.Bytes(), &response)
			require.NoError(t, err)

			assert.Equal(t, newName, response.Name)
			assert.NotNil(t, response.Metadata)

			t.Logf("✅ Updated license: %s", response.Name)
		})

		// Test 4: Validate License via Handler
		t.Run("validate_license", func(t *testing.T) {
			validateReq := ValidateLicenseRequest{
				LicenseKey:         testData.License.Key,
				MachineFingerprint: stringPtr("integration-test-machine-001"),
				Environment:        stringPtr("test"),
				MachineInfo: map[string]interface{}{
					"hostname": "integration-test-server",
					"os":       "linux",
					"memory":   "16GB",
					"cpu":      "Intel i7",
				},
			}

			jsonBytes, err := json.Marshal(validateReq)
			require.NoError(t, err)

			req := httptest.NewRequest("POST", "/api/v1/licenses/validate", bytes.NewBuffer(jsonBytes))
			req.Header.Set("Content-Type", "application/json")

			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Request = req
			c.Set("account_id", uuid.MustParse(testData.Account.ID))

			handler.ValidatePostHandler(c)

			require.Equal(t, 200, w.Code, "Expected license validation success")

			var response ValidateLicenseResponse
			err = json.Unmarshal(w.Body.Bytes(), &response)
			require.NoError(t, err)

			assert.True(t, response.Valid, "License should be valid")
			assert.NotNil(t, response.License, "Should return license data")
			assert.NotNil(t, response.Policy, "Should return policy data")
			assert.NotNil(t, response.Account, "Should return account data")
			assert.Equal(t, 1, response.MachinesUsed, "Should have 1 machine registered")

			t.Logf("✅ Validated license successfully - Machines used: %d/%d",
				response.MachinesUsed, response.MachinesAllowed)
		})

		t.Logf("✅ Complete license workflow test passed")
	})

	t.Run("license_validation_scenarios", func(t *testing.T) {
		// Test various license validation scenarios
		db, err := setupTestDatabaseWithMigrations(t)
		if err != nil {
			t.Skipf("Skipping test due to database setup error: %v", err)
			return
		}

		serviceCoordinator, err := createTestServiceCoordinator(t)
		if err != nil {
			t.Fatalf("Failed to create service coordinator: %v", err)
		}

		defer cleanupTestData(t, db)

		testData, err := setupCompleteWorkflowTestData(ctx, serviceCoordinator)
		if err != nil {
			t.Fatalf("Failed to setup test data: %v", err)
		}

		handler := NewLicenseHandler(serviceCoordinator)

		// Test 1: Valid license validation
		t.Run("valid_license", func(t *testing.T) {
			validateReq := ValidateLicenseRequest{
				LicenseKey:         testData.License.Key,
				MachineFingerprint: stringPtr("valid-machine-001"),
				Environment:        stringPtr("test"),
			}

			response := performLicenseValidation(t, handler, validateReq, testData.Account.ID)

			assert.True(t, response.Valid)
			assert.NotNil(t, response.License)
			assert.Equal(t, testData.License.Key, response.License.(*LicenseResponse).Key)
		})

		// Test 2: Invalid license key
		t.Run("invalid_license_key", func(t *testing.T) {
			validateReq := ValidateLicenseRequest{
				LicenseKey:         "INVALID-LICENSE-KEY",
				MachineFingerprint: stringPtr("test-machine-002"),
				Environment:        stringPtr("test"),
			}

			w := performLicenseValidationRaw(t, handler, validateReq, testData.Account.ID)
			assert.Equal(t, 400, w.Code, "Should return bad request for invalid license")
		})

		// Test 3: Multiple machine registrations
		t.Run("multiple_machines", func(t *testing.T) {
			machines := []string{"machine-001", "machine-002", "machine-003"}

			for i, machineID := range machines {
				validateReq := ValidateLicenseRequest{
					LicenseKey:         testData.License.Key,
					MachineFingerprint: &machineID,
					Environment:        stringPtr("test"),
					MachineInfo: map[string]interface{}{
						"hostname": fmt.Sprintf("server-%03d", i+1),
						"os":       "linux",
					},
				}

				response := performLicenseValidation(t, handler, validateReq, testData.Account.ID)

				assert.True(t, response.Valid, "License should be valid for machine %s", machineID)
				assert.Equal(t, i+1, response.MachinesUsed, "Should track machine count correctly")

				t.Logf("✅ Machine %s registered - Total machines: %d", machineID, response.MachinesUsed)
			}
		})

		t.Logf("✅ License validation scenarios test passed")
	})

	t.Run("policy_enforcement_tests", func(t *testing.T) {
		// Test policy limits enforcement
		db, err := setupTestDatabaseWithMigrations(t)
		if err != nil {
			t.Skipf("Skipping test due to database setup error: %v", err)
			return
		}

		serviceCoordinator, err := createTestServiceCoordinator(t)
		if err != nil {
			t.Fatalf("Failed to create service coordinator: %v", err)
		}

		defer cleanupTestData(t, db)

		// Create test data with restrictive policy
		testData, err := setupRestrictivePolicyTestData(ctx, serviceCoordinator)
		if err != nil {
			t.Fatalf("Failed to setup test data: %v", err)
		}

		handler := NewLicenseHandler(serviceCoordinator)

		// Test machine limit enforcement
		t.Run("machine_limit_enforcement", func(t *testing.T) {
			maxMachines := 3 // Set in setupRestrictivePolicyTestData
			successfulValidations := 0

			// Try to register more machines than allowed
			for i := 1; i <= maxMachines+2; i++ {
				machineID := fmt.Sprintf("policy-test-machine-%03d", i)
				validateReq := ValidateLicenseRequest{
					LicenseKey:         testData.License.Key,
					MachineFingerprint: &machineID,
					Environment:        stringPtr("test"),
					MachineInfo: map[string]interface{}{
						"hostname": fmt.Sprintf("policy-server-%03d", i),
						"os":       "linux",
					},
				}

				w := performLicenseValidationRaw(t, handler, validateReq, testData.Account.ID)

				if w.Code == 200 {
					var response ValidateLicenseResponse
					json.Unmarshal(w.Body.Bytes(), &response)
					if response.Valid {
						successfulValidations++
					}
					t.Logf("Machine %d: Valid=%v, Used=%d, Allowed=%d",
						i, response.Valid, response.MachinesUsed, response.MachinesAllowed)
				} else {
					t.Logf("Machine %d: HTTP %d (rejected)", i, w.Code)
				}
			}

			assert.LessOrEqual(t, successfulValidations, maxMachines,
				"Should not exceed policy machine limit")
			t.Logf("✅ Policy enforcement: %d/%d machines allowed", successfulValidations, maxMachines)
		})

		// Test expired license
		t.Run("expired_license", func(t *testing.T) {
			// Create expired license
			expiredLicense := &entities.License{
				AccountID:     testData.Account.ID,
				ProductID:     testData.Product.ID,
				PolicyID:      testData.Policy.ID,
				EnvironmentID: &testData.Environment.ID,
				Key:           "EXPIRED-" + uuid.New().String()[0:8],
				Name:          "Expired Test License",
				Status:        entities.LicenseStatusActive,
				ExpiresAt:     testTimePtr(time.Now().Add(-24 * time.Hour)), // Expired yesterday
			}

			err := serviceCoordinator.Repositories.License().Create(ctx, expiredLicense)
			require.NoError(t, err)

			validateReq := ValidateLicenseRequest{
				LicenseKey:         expiredLicense.Key,
				MachineFingerprint: stringPtr("expired-test-machine"),
				Environment:        stringPtr("test"),
			}

			w := performLicenseValidationRaw(t, handler, validateReq, testData.Account.ID)

			// Should reject expired license
			assert.NotEqual(t, 200, w.Code, "Should reject expired license")
			t.Logf("✅ Expired license correctly rejected with HTTP %d", w.Code)
		})

		t.Logf("✅ Policy enforcement tests passed")
	})

	t.Run("error_handling_tests", func(t *testing.T) {
		// Test error handling scenarios
		db, err := setupTestDatabaseWithMigrations(t)
		if err != nil {
			t.Skipf("Skipping test due to database setup error: %v", err)
			return
		}

		serviceCoordinator, err := createTestServiceCoordinator(t)
		if err != nil {
			t.Fatalf("Failed to create service coordinator: %v", err)
		}

		defer cleanupTestData(t, db)

		testData, err := setupCompleteWorkflowTestData(ctx, serviceCoordinator)
		if err != nil {
			t.Fatalf("Failed to setup test data: %v", err)
		}

		handler := NewLicenseHandler(serviceCoordinator)

		// Test 1: Invalid JSON request
		t.Run("invalid_json", func(t *testing.T) {
			req := httptest.NewRequest("POST", "/api/v1/licenses", bytes.NewBufferString("invalid json"))
			req.Header.Set("Content-Type", "application/json")

			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Request = req
			c.Set("account_id", uuid.MustParse(testData.Account.ID))

			handler.CreateLicense(c)

			assert.Equal(t, 400, w.Code, "Should return bad request for invalid JSON")

			var errorResponse map[string]interface{}
			json.Unmarshal(w.Body.Bytes(), &errorResponse)
			assert.Equal(t, "validation_failed", errorResponse["error"])
		})

		// Test 2: Missing required fields
		t.Run("missing_required_fields", func(t *testing.T) {
			createReq := LicenseCreateRequest{
				// Missing Name and PolicyID (required fields)
				Metadata: map[string]interface{}{"test": true},
			}

			jsonBytes, _ := json.Marshal(createReq)
			req := httptest.NewRequest("POST", "/api/v1/licenses", bytes.NewBuffer(jsonBytes))
			req.Header.Set("Content-Type", "application/json")

			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Request = req
			c.Set("account_id", uuid.MustParse(testData.Account.ID))

			handler.CreateLicense(c)

			assert.Equal(t, 400, w.Code, "Should return bad request for missing required fields")
		})

		// Test 3: Invalid UUID format
		t.Run("invalid_uuid_format", func(t *testing.T) {
			req := httptest.NewRequest("GET", "/api/v1/licenses/invalid-uuid", nil)
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Request = req
			c.Params = []gin.Param{{Key: "id", Value: "invalid-uuid"}}
			c.Set("account_id", uuid.MustParse(testData.Account.ID))

			handler.GetLicense(c)

			assert.Equal(t, 400, w.Code, "Should return bad request for invalid UUID")

			var errorResponse map[string]interface{}
			json.Unmarshal(w.Body.Bytes(), &errorResponse)
			assert.Equal(t, "invalid_parameter", errorResponse["error"])
		})

		// Test 4: License not found
		t.Run("license_not_found", func(t *testing.T) {
			nonExistentID := uuid.New().String()
			req := httptest.NewRequest("GET", "/api/v1/licenses/"+nonExistentID, nil)
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Request = req
			c.Params = []gin.Param{{Key: "id", Value: nonExistentID}}
			c.Set("account_id", uuid.MustParse(testData.Account.ID))

			handler.GetLicense(c)

			assert.Equal(t, 404, w.Code, "Should return not found for non-existent license")

			var errorResponse map[string]interface{}
			json.Unmarshal(w.Body.Bytes(), &errorResponse)
			assert.Equal(t, "not_found", errorResponse["error"])
		})

		// Test 5: Unauthorized access (no account_id)
		t.Run("unauthorized_access", func(t *testing.T) {
			req := httptest.NewRequest("GET", "/api/v1/licenses", nil)
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Request = req
			// Don't set account_id

			handler.ListLicenses(c)

			assert.Equal(t, 401, w.Code, "Should return unauthorized without account_id")

			var errorResponse map[string]interface{}
			json.Unmarshal(w.Body.Bytes(), &errorResponse)
			assert.Equal(t, "unauthorized", errorResponse["error"])
		})

		t.Logf("✅ Error handling tests passed")
	})

	t.Run("list_licenses_tests", func(t *testing.T) {
		// Test license listing functionality
		db, err := setupTestDatabaseWithMigrations(t)
		if err != nil {
			t.Skipf("Skipping test due to database setup error: %v", err)
			return
		}

		serviceCoordinator, err := createTestServiceCoordinator(t)
		if err != nil {
			t.Fatalf("Failed to create service coordinator: %v", err)
		}

		defer cleanupTestData(t, db)

		testData, err := setupCompleteWorkflowTestData(ctx, serviceCoordinator)
		if err != nil {
			t.Fatalf("Failed to setup test data: %v", err)
		}

		handler := NewLicenseHandler(serviceCoordinator)

		// Test basic listing
		t.Run("basic_list", func(t *testing.T) {
			req := httptest.NewRequest("GET", "/api/v1/licenses", nil)
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Request = req
			c.Set("account_id", uuid.MustParse(testData.Account.ID))

			handler.ListLicenses(c)

			assert.Equal(t, 200, w.Code, "Should return success for license listing")

			var response LicenseListResponse
			err := json.Unmarshal(w.Body.Bytes(), &response)
			require.NoError(t, err)

			assert.NotNil(t, response.Licenses, "Should return licenses array")
			assert.NotNil(t, response.Pagination, "Should return pagination info")
			assert.Equal(t, 1, response.Pagination.Page, "Should default to page 1")
			assert.Equal(t, 25, response.Pagination.PerPage, "Should default to 25 per page")
		})

		// Test pagination parameters
		t.Run("pagination", func(t *testing.T) {
			req := httptest.NewRequest("GET", "/api/v1/licenses?page=2&per_page=10", nil)
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Request = req
			c.Set("account_id", uuid.MustParse(testData.Account.ID))

			handler.ListLicenses(c)

			assert.Equal(t, 200, w.Code, "Should return success")

			var response LicenseListResponse
			err := json.Unmarshal(w.Body.Bytes(), &response)
			require.NoError(t, err)

			assert.Equal(t, 2, response.Pagination.Page, "Should respect page parameter")
			assert.Equal(t, 10, response.Pagination.PerPage, "Should respect per_page parameter")
		})

		t.Logf("✅ List licenses tests passed")
	})
}
