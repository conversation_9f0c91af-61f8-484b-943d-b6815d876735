package handlers

import (
	"bytes"
	"encoding/json"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestEntitlementHandlerBasic(t *testing.T) {
	// This is a basic unit test that doesn't require database connection
	gin.SetMode(gin.TestMode)
	
	t.Run("handler_creation", func(t *testing.T) {
		// Test that we can create the handler without panicking
		// This uses a nil service coordinator which is fine for this basic test
		handler := NewEntitlementHandler(nil)
		assert.NotNil(t, handler)
	})

	t.Run("request_validation", func(t *testing.T) {
		// Test basic request structure validation
		requestBody := map[string]interface{}{
			"data": map[string]interface{}{
				"type": "entitlements",
				"attributes": map[string]interface{}{
					"name": "Test Entitlement",
					"code": "TEST_CODE",
				},
			},
		}

		body, err := json.Marshal(requestBody)
		require.NoError(t, err)

		req := httptest.NewRequest("POST", "/api/v1/entitlements", bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")

		assert.Equal(t, "POST", req.Method)
		assert.Equal(t, "/api/v1/entitlements", req.URL.Path)
		assert.Equal(t, "application/json", req.Header.Get("Content-Type"))
	})

	t.Run("json_parsing", func(t *testing.T) {
		// Test that our request structure can be parsed
		jsonStr := `{
			"data": {
				"type": "entitlements",
				"attributes": {
					"name": "Test Entitlement",
					"code": "TEST_CODE",
					"metadata": {
						"description": "Test description"
					}
				}
			}
		}`

		var requestBody map[string]interface{}
		err := json.Unmarshal([]byte(jsonStr), &requestBody)
		require.NoError(t, err)

		data := requestBody["data"].(map[string]interface{})
		attributes := data["attributes"].(map[string]interface{})
		
		assert.Equal(t, "entitlements", data["type"])
		assert.Equal(t, "Test Entitlement", attributes["name"])
		assert.Equal(t, "TEST_CODE", attributes["code"])
	})

	t.Run("uuid_validation", func(t *testing.T) {
		// Test UUID parsing functionality
		validUUID := "550e8400-e29b-41d4-a716-************"
		invalidUUID := "invalid-uuid"

		// Valid UUID should parse
		parsedUUID, err := uuid.Parse(validUUID)
		assert.NoError(t, err)
		assert.Equal(t, validUUID, parsedUUID.String())

		// Invalid UUID should fail
		_, err = uuid.Parse(invalidUUID)
		assert.Error(t, err)
	})
}

func TestEntitlementRequestStructure(t *testing.T) {
	t.Run("entitlement_request_binding", func(t *testing.T) {
		// Test the EntitlementRequest structure
		jsonStr := `{
			"data": {
				"type": "entitlements",
				"attributes": {
					"name": "Test Entitlement",
					"code": "TEST_CODE",
					"metadata": {
						"description": "Test description",
						"version": "1.0"
					}
				},
				"relationships": {
					"environment": {
						"data": {
							"type": "environments",
							"id": "550e8400-e29b-41d4-a716-************"
						}
					}
				}
			}
		}`

		// Create a gin context with the request body
		gin.SetMode(gin.TestMode)
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		
		req := httptest.NewRequest("POST", "/", bytes.NewBufferString(jsonStr))
		req.Header.Set("Content-Type", "application/json")
		c.Request = req

		var req_struct EntitlementRequest
		err := c.ShouldBindJSON(&req_struct)
		
		require.NoError(t, err)
		assert.Equal(t, "entitlements", req_struct.Data.Type)
		assert.Equal(t, "Test Entitlement", req_struct.Data.Attributes.Name)
		assert.Equal(t, "TEST_CODE", req_struct.Data.Attributes.Code)
		assert.NotNil(t, req_struct.Data.Attributes.Metadata)
		
		if req_struct.Data.Relationships != nil && req_struct.Data.Relationships.Environment != nil {
			assert.Equal(t, "550e8400-e29b-41d4-a716-************", req_struct.Data.Relationships.Environment.Data.ID)
		}
	})

	t.Run("missing_required_fields", func(t *testing.T) {
		// Test validation of required fields
		testCases := []struct {
			name        string
			jsonStr     string
			shouldError bool
		}{
			{
				name: "missing_name",
				jsonStr: `{
					"data": {
						"type": "entitlements",
						"attributes": {
							"code": "TEST_CODE"
						}
					}
				}`,
				shouldError: true,
			},
			{
				name: "missing_code",
				jsonStr: `{
					"data": {
						"type": "entitlements",
						"attributes": {
							"name": "Test Entitlement"
						}
					}
				}`,
				shouldError: true,
			},
			{
				name: "missing_type",
				jsonStr: `{
					"data": {
						"attributes": {
							"name": "Test Entitlement",
							"code": "TEST_CODE"
						}
					}
				}`,
				shouldError: true,
			},
			{
				name: "valid_minimal",
				jsonStr: `{
					"data": {
						"type": "entitlements",
						"attributes": {
							"name": "Test Entitlement",
							"code": "TEST_CODE"
						}
					}
				}`,
				shouldError: false,
			},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				gin.SetMode(gin.TestMode)
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				
				req := httptest.NewRequest("POST", "/", bytes.NewBufferString(tc.jsonStr))
				req.Header.Set("Content-Type", "application/json")
				c.Request = req

				var req_struct EntitlementRequest
				err := c.ShouldBindJSON(&req_struct)
				
				if tc.shouldError {
					assert.Error(t, err)
				} else {
					assert.NoError(t, err)
				}
			})
		}
	})
}

func TestMockAccountMiddleware(t *testing.T) {
	t.Run("account_middleware_sets_context", func(t *testing.T) {
		gin.SetMode(gin.TestMode)
		
		testAccountID := "550e8400-e29b-41d4-a716-************"
		middleware := mockAccountMiddleware(testAccountID)
		
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		
		// Execute middleware
		middleware(c)
		
		// Check that account_id was set in context
		accountID, exists := c.Get("account_id")
		assert.True(t, exists)
		assert.Equal(t, testAccountID, accountID.(uuid.UUID).String())
	})

	t.Run("empty_account_id_middleware", func(t *testing.T) {
		gin.SetMode(gin.TestMode)
		
		middleware := mockAccountMiddleware("")
		
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		
		// Execute middleware
		middleware(c)
		
		// Check that account_id was not set
		_, exists := c.Get("account_id")
		assert.False(t, exists)
	})
}

// Test that the test suite can be instantiated
func TestTestSuiteCreation(t *testing.T) {
	suite := NewEntitlementIntegrationTestSuite(t)
	assert.NotNil(t, suite)
	assert.Equal(t, t, suite.t)
}