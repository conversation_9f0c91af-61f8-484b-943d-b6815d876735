package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/adapters/http/middleware"
	"github.com/gokeys/gokeys/internal/adapters/http/responses"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/gokeys/gokeys/internal/domain/services"
	"github.com/gokeys/gokeys/internal/domain/services/events"
	"github.com/google/uuid"
)

type ProductHandler struct {
	serviceCoordinator *services.ServiceCoordinator
}

func NewProductHandler(serviceCoordinator *services.ServiceCoordinator) *ProductHandler {
	return &ProductHandler{
		serviceCoordinator: serviceCoordinator,
	}
}

type ProductRequest struct {
	Data struct {
		Type       string `json:"type" binding:"required"`
		Attributes struct {
			Name                 string                 `json:"name" binding:"required"`
			Code                 *string                `json:"code,omitempty"`
			DistributionStrategy *string                `json:"distribution_strategy,omitempty"`
			URL                  *string                `json:"url,omitempty"`
			Platforms            []string               `json:"platforms,omitempty"`
			Metadata             map[string]interface{} `json:"metadata,omitempty"`
		} `json:"attributes"`
		Relationships *struct {
			Environment *struct {
				Data *struct {
					Type string `json:"type"`
					ID   string `json:"id"`
				} `json:"data"`
			} `json:"environment,omitempty"`
		} `json:"relationships,omitempty"`
	} `json:"data"`
}

func (h *ProductHandler) ListProductsHandler(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		responses.RenderUnauthorized(c, "Account ID not found in token")
		return
	}

	// Parse pagination
	page := 1
	limit := 25
	if p := c.Query("page"); p != "" {
		if parsed, err := strconv.Atoi(p); err == nil && parsed > 0 {
			page = parsed
		}
	}
	if l := c.Query("limit"); l != "" {
		if parsed, err := strconv.Atoi(l); err == nil && parsed > 0 && parsed <= 100 {
			limit = parsed
		}
	}

	// Build filter
	filter := repositories.ListFilter{
		Page:      page,
		PageSize:  limit,
		SortBy:    c.DefaultQuery("sort_by", "created_at"),
		SortOrder: c.DefaultQuery("sort_order", "DESC"),
		Search:    c.Query("search"),
		AccountID: &accountID,
		Filters:   make(map[string]interface{}),
	}

	// Get products from repository
	products, total, err := h.serviceCoordinator.Repositories.Product().List(c.Request.Context(), filter)
	if err != nil {
		responses.RenderInternalError(c, "Failed to retrieve products: "+err.Error())
		return
	}

	response := gin.H{
		"data": products,
		"meta": gin.H{
			"page":        page,
			"limit":       limit,
			"total":       total,
			"total_pages": (total + int64(limit) - 1) / int64(limit),
		},
	}

	c.JSON(http.StatusOK, response)
}

func (h *ProductHandler) GetProductHandler(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		responses.RenderUnauthorized(c, "Account ID not found in token")
		return
	}

	productIDStr := c.Param("id")
	productID, err := uuid.Parse(productIDStr)
	if err != nil {
		responses.RenderBadRequest(c, "Invalid product ID format")
		return
	}

	// Get product from repository by ID
	product, err := h.serviceCoordinator.Repositories.Product().GetByID(c.Request.Context(), productID)
	if err != nil {
		responses.RenderNotFound(c, "Product not found")
		return
	}

	// Verify product belongs to the account
	if product.AccountID != accountID.String() {
		responses.RenderNotFound(c, "Product not found")
		return
	}

	response := gin.H{
		"data": product,
	}

	c.JSON(http.StatusOK, response)
}

func (h *ProductHandler) CreateProductHandler(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		responses.RenderUnauthorized(c, "Account ID not found in token")
		return
	}

	var req ProductRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responses.RenderBadRequest(c, "Invalid request format: "+err.Error())
		return
	}

	// Create product through service layer
	product := &entities.Product{
		AccountID:            accountID.String(),
		Name:                 req.Data.Attributes.Name,
		DistributionStrategy: req.Data.Attributes.DistributionStrategy,
		Metadata:             req.Data.Attributes.Metadata,
		CreatedAt:            time.Now(),
		UpdatedAt:            time.Now(),
	}

	// Set optional string fields
	if req.Data.Attributes.Code != nil {
		product.Code = *req.Data.Attributes.Code
	}
	if req.Data.Attributes.URL != nil {
		product.URL = *req.Data.Attributes.URL
	}

	// Set platforms
	if req.Data.Attributes.Platforms != nil {
		product.Platforms = entities.ProductPlatforms{
			Supported: req.Data.Attributes.Platforms,
		}
	}

	// Set environment ID if provided
	if req.Data.Relationships != nil && req.Data.Relationships.Environment != nil {
		product.EnvironmentID = &req.Data.Relationships.Environment.Data.ID
	}

	// Save product to repository
	if err := h.serviceCoordinator.Repositories.Product().Create(c.Request.Context(), product); err != nil {
		responses.RenderInternalError(c, "Failed to create product: "+err.Error())
		return
	}

	// Broadcast event
	account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	h.serviceCoordinator.Events.BroadcastEvent(
		c.Request.Context(),
		events.EventProductCreated,
		account,
		events.MakeEventResource(product),
		events.EventMeta{},
	)

	response := gin.H{
		"data": product,
	}

	c.Header("Location", "/api/v1/products/"+product.ID)
	c.JSON(http.StatusCreated, response)
}

func (h *ProductHandler) UpdateProductHandler(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		responses.RenderUnauthorized(c, "Account ID not found in token")
		return
	}

	productIDStr := c.Param("id")
	productID, err := uuid.Parse(productIDStr)
	if err != nil {
		responses.RenderBadRequest(c, "Invalid product ID format")
		return
	}

	var req ProductRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responses.RenderBadRequest(c, "Invalid request format: "+err.Error())
		return
	}

	// Get existing product
	product, err := h.serviceCoordinator.Repositories.Product().GetByID(c.Request.Context(), productID)
	if err != nil {
		responses.RenderNotFound(c, "Product not found")
		return
	}

	// Verify product belongs to the account
	if product.AccountID != accountID.String() {
		responses.RenderNotFound(c, "Product not found")
		return
	}

	// Update product fields
	product.Name = req.Data.Attributes.Name
	if req.Data.Attributes.Code != nil {
		product.Code = *req.Data.Attributes.Code
	}
	if req.Data.Attributes.DistributionStrategy != nil {
		product.DistributionStrategy = req.Data.Attributes.DistributionStrategy
	}
	if req.Data.Attributes.URL != nil {
		product.URL = *req.Data.Attributes.URL
	}
	if req.Data.Attributes.Platforms != nil {
		product.Platforms = entities.ProductPlatforms{
			Supported: req.Data.Attributes.Platforms,
		}
	}
	if req.Data.Attributes.Metadata != nil {
		product.Metadata = req.Data.Attributes.Metadata
	}
	product.UpdatedAt = time.Now()

	// Save to repository
	if err := h.serviceCoordinator.Repositories.Product().Update(c.Request.Context(), product); err != nil {
		responses.RenderInternalError(c, "Failed to update product: "+err.Error())
		return
	}

	// Broadcast event
	account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	h.serviceCoordinator.Events.BroadcastEvent(
		c.Request.Context(),
		events.EventProductUpdated,
		account,
		events.MakeEventResource(product),
		events.EventMeta{},
	)

	response := gin.H{
		"data": product,
	}

	c.JSON(http.StatusOK, response)
}

func (h *ProductHandler) DeleteProductHandler(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		responses.RenderUnauthorized(c, "Account ID not found in token")
		return
	}

	productIDStr := c.Param("id")
	productID, err := uuid.Parse(productIDStr)
	if err != nil {
		responses.RenderBadRequest(c, "Invalid product ID format")
		return
	}

	// Get product from repository
	product, err := h.serviceCoordinator.Repositories.Product().GetByID(c.Request.Context(), productID)
	if err != nil {
		responses.RenderNotFound(c, "Product not found")
		return
	}

	// Verify product belongs to the account
	if product.AccountID != accountID.String() {
		responses.RenderNotFound(c, "Product not found")
		return
	}

	// Delete product
	if err := h.serviceCoordinator.Repositories.Product().Delete(c.Request.Context(), productID); err != nil {
		responses.RenderInternalError(c, "Failed to delete product: "+err.Error())
		return
	}

	// Broadcast event
	account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	h.serviceCoordinator.Events.BroadcastEvent(
		c.Request.Context(),
		events.EventProductDeleted,
		account,
		events.MakeEventResource(product),
		events.EventMeta{},
	)

	c.Status(http.StatusNoContent)
}