package handlers

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"
)

// TestMigrationSystemDemo demonstrates how to use the migration system in tests
func TestMigrationSystemDemo(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping migration demo test in short mode")
	}

	t.Run("migration_system_setup", func(t *testing.T) {
		// Setup database with all migrations applied
		db, err := setupTestDatabaseWithMigrations(t)
		if err != nil {
			t.Skipf("Skipping test due to database setup: %v", err)
			return
		}

		// Verify that all major tables exist
		tables := []string{
			"accounts", "environments", "users", "groups", "products",
			"policies", "entitlements", "licenses", "machines",
			"machine_components", "machine_processes", "tokens",
			"sessions", "roles", "permissions", "second_factors",
			"webhook_endpoints", "webhook_events",
		}

		for _, table := range tables {
			var exists bool
			err := db.Raw(`
				SELECT EXISTS (
					SELECT FROM information_schema.tables 
					WHERE table_name = ?
				)
			`, table).Scan(&exists).Error
			
			require.NoError(t, err, "Failed to check table %s", table)
			assert.True(t, exists, "Table %s should exist after migrations", table)
		}

		// Verify junction tables exist
		junctionTables := []string{
			"license_users", "license_entitlements", "policy_entitlements",
			"group_owners", "group_permissions", "role_permissions", "token_permissions",
		}

		for _, table := range junctionTables {
			var exists bool
			err := db.Raw(`
				SELECT EXISTS (
					SELECT FROM information_schema.tables 
					WHERE table_name = ?
				)
			`, table).Scan(&exists).Error
			
			require.NoError(t, err, "Failed to check junction table %s", table)
			assert.True(t, exists, "Junction table %s should exist after migrations", table)
		}

		t.Log("✅ All tables created successfully by migrations")
	})

	t.Run("service_coordinator_with_migrations", func(t *testing.T) {
		// Create real service coordinator with migrated database
		serviceCoordinator, err := createTestServiceCoordinator(t)
		if err != nil {
			t.Skipf("Skipping test due to service coordinator setup: %v", err)
			return
		}

		// Verify repositories are working
		require.NotNil(t, serviceCoordinator.Repositories.Account())
		require.NotNil(t, serviceCoordinator.Repositories.License())
		require.NotNil(t, serviceCoordinator.Repositories.Product())
		require.NotNil(t, serviceCoordinator.Repositories.Policy())
		require.NotNil(t, serviceCoordinator.Repositories.Machine())

		t.Log("✅ Service coordinator and repositories working with migrated database")
	})

	t.Run("handler_with_real_database", func(t *testing.T) {
		// Create service coordinator
		serviceCoordinator, err := createTestServiceCoordinator(t)
		if err != nil {
			t.Skipf("Skipping test due to service coordinator setup: %v", err)
			return
		}

		// Create handlers with real service coordinator
		licenseHandler := NewLicenseHandler(serviceCoordinator)
		require.NotNil(t, licenseHandler)

		// Verify handler has access to real services
		// This demonstrates that handlers can now work with real database
		t.Log("✅ Handlers created successfully with real database services")
	})
}

// TestMigrationSystemAdvanced shows advanced usage patterns
func TestMigrationSystemAdvanced(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping advanced migration test in short mode")
	}

	t.Run("transaction_based_test", func(t *testing.T) {
		// Use transaction-based testing for isolated tests
		withTestTransaction(t, func(t *testing.T, tx *gorm.DB) {
			// Test data will be automatically rolled back
			err := tx.Exec(`
				INSERT INTO accounts (id, name, slug, email) 
				VALUES ('test-id', 'Test Account', 'test-slug', '<EMAIL>')
			`).Error
			require.NoError(t, err)

			// Verify data exists in transaction
			var count int64
			err = tx.Raw("SELECT COUNT(*) FROM accounts WHERE slug = 'test-slug'").Scan(&count).Error
			require.NoError(t, err)
			assert.Equal(t, int64(1), count)

			t.Log("✅ Transaction-based test working - data will be rolled back")
		})
	})
}