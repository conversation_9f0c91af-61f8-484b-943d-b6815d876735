package handlers

import (
	"bytes"
	"encoding/json"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"github.com/gokeys/gokeys/internal/domain/entities"
)

// TestEntitlementHandlerWithRealDB tests the entitlement handler with a real database connection
func TestEntitlementHandlerWithRealDB(t *testing.T) {
	// Skip if not in integration test mode
	if testing.Short() {
		t.Skip("Skipping database integration test in short mode")
	}

	// Connect to test database
	dsn := "postgres://gokeys:gokeys_dev_password@localhost:5432/gokeys_test?sslmode=disable"
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	require.NoError(t, err, "Failed to connect to test database")

	// Create simple test tables
	err = db.Exec(`
		CREATE TABLE IF NOT EXISTS test_accounts (
			id VARCHAR(36) PRIMARY KEY,
			name VARCHAR(255) NOT NULL,
			slug VARCHAR(255) UNIQUE NOT NULL,
			email VARCHAR(255) UNIQUE NOT NULL,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		);
		
		CREATE TABLE IF NOT EXISTS test_environments (
			id VARCHAR(36) PRIMARY KEY,
			account_id VARCHAR(36) NOT NULL,
			name VARCHAR(255) NOT NULL,
			code VARCHAR(255) NOT NULL,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		);
		
		CREATE TABLE IF NOT EXISTS test_entitlements (
			id VARCHAR(36) PRIMARY KEY,
			account_id VARCHAR(36) NOT NULL,
			environment_id VARCHAR(36),
			name VARCHAR(255) NOT NULL,
			code VARCHAR(255) NOT NULL,
			metadata JSONB,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		);
	`).Error
	require.NoError(t, err, "Failed to create test tables")

	// Clean up function
	defer func() {
		db.Exec("DROP TABLE IF EXISTS test_entitlements CASCADE")
		db.Exec("DROP TABLE IF EXISTS test_environments CASCADE") 
		db.Exec("DROP TABLE IF EXISTS test_accounts CASCADE")
		sqlDB, _ := db.DB()
		sqlDB.Close()
	}()

	// Insert test data
	testAccount := &entities.Account{
		ID:    uuid.New().String(),
		Name:  "Test Account",
		Slug:  "test-account",
		Email: "<EMAIL>",
	}

	testEnvironment := &entities.Environment{
		ID:        uuid.New().String(),
		AccountID: testAccount.ID,
		Name:      "Test Environment",
		Code:      "test",
	}

	// Insert using raw SQL to avoid GORM entity issues
	err = db.Exec(`
		INSERT INTO test_accounts (id, name, slug, email) 
		VALUES (?, ?, ?, ?)`,
		testAccount.ID, testAccount.Name, testAccount.Slug, testAccount.Email).Error
	require.NoError(t, err)

	err = db.Exec(`
		INSERT INTO test_environments (id, account_id, name, code) 
		VALUES (?, ?, ?, ?)`,
		testEnvironment.ID, testEnvironment.AccountID, testEnvironment.Name, testEnvironment.Code).Error
	require.NoError(t, err)

	t.Run("database_connection_works", func(t *testing.T) {
		var count int64
		err := db.Raw("SELECT COUNT(*) FROM test_accounts").Scan(&count).Error
		assert.NoError(t, err)
		assert.Equal(t, int64(1), count)
	})

	t.Run("can_insert_entitlement", func(t *testing.T) {
		entitlementID := uuid.New().String()
		err := db.Exec(`
			INSERT INTO test_entitlements (id, account_id, environment_id, name, code, metadata) 
			VALUES (?, ?, ?, ?, ?, ?)`,
			entitlementID, testAccount.ID, testEnvironment.ID, "Test Entitlement", "TEST_CODE", 
			`{"description": "Test entitlement"}`).Error
		assert.NoError(t, err)

		// Verify insertion
		var retrievedName string
		err = db.Raw("SELECT name FROM test_entitlements WHERE id = ?", entitlementID).Scan(&retrievedName).Error
		assert.NoError(t, err)
		assert.Equal(t, "Test Entitlement", retrievedName)
	})

	t.Run("basic_http_request_structure", func(t *testing.T) {
		// Test basic HTTP request handling without full service coordinator
		gin.SetMode(gin.TestMode)

		// Create a simple request body
		requestBody := map[string]interface{}{
			"data": map[string]interface{}{
				"type": "entitlements",
				"attributes": map[string]interface{}{
					"name": "Test Entitlement",
					"code": "TEST_CODE",
					"metadata": map[string]interface{}{
						"description": "Test description",
					},
				},
			},
		}

		body, err := json.Marshal(requestBody)
		require.NoError(t, err)

		req := httptest.NewRequest("POST", "/api/v1/entitlements", bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer test-token")

		// Create response recorder
		w := httptest.NewRecorder()
		
		// Create basic gin context
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// Test request parsing
		var parsedRequest EntitlementRequest
		err = c.ShouldBindJSON(&parsedRequest)
		assert.NoError(t, err)
		assert.Equal(t, "entitlements", parsedRequest.Data.Type)
		assert.Equal(t, "Test Entitlement", parsedRequest.Data.Attributes.Name)
		assert.Equal(t, "TEST_CODE", parsedRequest.Data.Attributes.Code)
	})
}

// TestDatabaseConnectivity tests basic database connectivity
func TestDatabaseConnectivity(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping database connectivity test in short mode")
	}

	dsn := "postgres://gokeys:gokeys_dev_password@localhost:5432/gokeys_test?sslmode=disable"
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	
	assert.NoError(t, err, "Should connect to database")
	
	if db != nil {
		sqlDB, _ := db.DB()
		defer sqlDB.Close()
		
		// Test basic query
		var result int
		err = db.Raw("SELECT 1").Scan(&result).Error
		assert.NoError(t, err)
		assert.Equal(t, 1, result)
	}
}