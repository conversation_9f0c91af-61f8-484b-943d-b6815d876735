package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/adapters/http/middleware"
	"github.com/gokeys/gokeys/internal/adapters/http/responses"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/gokeys/gokeys/internal/domain/services"
	"github.com/gokeys/gokeys/internal/domain/services/events"
	"github.com/google/uuid"
)

type PolicyHandler struct {
	serviceCoordinator *services.ServiceCoordinator
}

func NewPolicyHandler(serviceCoordinator *services.ServiceCoordinator) *PolicyHandler {
	return &PolicyHandler{
		serviceCoordinator: serviceCoordinator,
	}
}

// Go-style request structs - simple and clear
type PolicyCreateRequest struct {
	Name             string  `json:"name" binding:"required,min=1,max=255"`
	Description      string  `json:"description,omitempty"`
	ProductID        string  `json:"product_id" binding:"required,uuid"`
	EnvironmentID    *string `json:"environment_id,omitempty" binding:"omitempty,uuid"`
	Duration         *int    `json:"duration,omitempty" binding:"omitempty,min=0"`
	Strict           bool    `json:"strict"`
	Floating         bool    `json:"floating"`
	RequireHeartbeat bool    `json:"require_heartbeat"`

	// Machine limits
	MaxMachines  *int `json:"max_machines,omitempty" binding:"omitempty,min=0"`
	MaxProcesses *int `json:"max_processes,omitempty" binding:"omitempty,min=0"`
	MaxUsers     *int `json:"max_users,omitempty" binding:"omitempty,min=0"`
	MaxCores     *int `json:"max_cores,omitempty" binding:"omitempty,min=0"`
	MaxUses      *int `json:"max_uses,omitempty" binding:"omitempty,min=0"`

	// Advanced settings (simplified)
	Scheme                    string `json:"scheme,omitempty" binding:"omitempty,oneof=ED25519_SIGN RSA_PKCS1_SIGN RSA_PSS_SIGN"`
	HeartbeatDuration         *int   `json:"heartbeat_duration,omitempty" binding:"omitempty,min=0"`
	MachineUniquenessStrategy string `json:"machine_uniqueness_strategy,omitempty" binding:"omitempty,oneof=UNIQUE_PER_ACCOUNT UNIQUE_PER_PRODUCT UNIQUE_PER_POLICY UNIQUE_PER_LICENSE"`
	ExpirationStrategy        string `json:"expiration_strategy,omitempty" binding:"omitempty,oneof=RESTRICT_ACCESS REVOKE_ACCESS MAINTAIN_ACCESS"`
	OverageStrategy           string `json:"overage_strategy,omitempty" binding:"omitempty,oneof=NO_OVERAGE ALWAYS_ALLOW_OVERAGE ALLOW_1_25X_OVERAGE ALLOW_1_5X_OVERAGE ALLOW_2X_OVERAGE"`

	// Check-in settings
	RequireCheckIn       bool    `json:"require_check_in"`
	CheckInInterval      *string `json:"check_in_interval,omitempty"`
	CheckInIntervalCount *int    `json:"check_in_interval_count,omitempty" binding:"omitempty,min=0"`

	// Pool and activation limits
	UsePool          bool `json:"use_pool"`
	MaxActivations   *int `json:"max_activations,omitempty" binding:"omitempty,min=0"`
	MaxDeactivations *int `json:"max_deactivations,omitempty" binding:"omitempty,min=0"`

	Protected bool                   `json:"protected"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

type PolicyUpdateRequest struct {
	Name             *string `json:"name,omitempty" binding:"omitempty,min=1,max=255"`
	Description      *string `json:"description,omitempty"`
	Duration         *int    `json:"duration,omitempty" binding:"omitempty,min=0"`
	Strict           *bool   `json:"strict,omitempty"`
	Floating         *bool   `json:"floating,omitempty"`
	RequireHeartbeat *bool   `json:"require_heartbeat,omitempty"`

	// Machine limits
	MaxMachines  *int `json:"max_machines,omitempty" binding:"omitempty,min=0"`
	MaxProcesses *int `json:"max_processes,omitempty" binding:"omitempty,min=0"`
	MaxUsers     *int `json:"max_users,omitempty" binding:"omitempty,min=0"`
	MaxCores     *int `json:"max_cores,omitempty" binding:"omitempty,min=0"`
	MaxUses      *int `json:"max_uses,omitempty" binding:"omitempty,min=0"`

	// Advanced settings
	Scheme                    *string `json:"scheme,omitempty" binding:"omitempty,oneof=ED25519_SIGN RSA_PKCS1_SIGN RSA_PSS_SIGN"`
	HeartbeatDuration         *int    `json:"heartbeat_duration,omitempty" binding:"omitempty,min=0"`
	MachineUniquenessStrategy *string `json:"machine_uniqueness_strategy,omitempty"`
	ExpirationStrategy        *string `json:"expiration_strategy,omitempty"`
	OverageStrategy           *string `json:"overage_strategy,omitempty"`

	// Check-in settings
	RequireCheckIn       *bool   `json:"require_check_in,omitempty"`
	CheckInInterval      *string `json:"check_in_interval,omitempty"`
	CheckInIntervalCount *int    `json:"check_in_interval_count,omitempty" binding:"omitempty,min=0"`

	// Pool and activation limits
	UsePool          *bool `json:"use_pool,omitempty"`
	MaxActivations   *int  `json:"max_activations,omitempty" binding:"omitempty,min=0"`
	MaxDeactivations *int  `json:"max_deactivations,omitempty" binding:"omitempty,min=0"`

	Protected *bool                  `json:"protected,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// Go-style response structs
type PolicyResponse struct {
	ID                        string                 `json:"id"`
	Name                      string                 `json:"name"`
	Description               string                 `json:"description,omitempty"`
	ProductID                 string                 `json:"product_id"`
	EnvironmentID             *string                `json:"environment_id,omitempty"`
	Duration                  *int                   `json:"duration,omitempty"`
	Strict                    bool                   `json:"strict"`
	Floating                  bool                   `json:"floating"`
	RequireHeartbeat          bool                   `json:"require_heartbeat"`
	MaxMachines               *int                   `json:"max_machines,omitempty"`
	MaxProcesses              *int                   `json:"max_processes,omitempty"`
	MaxUsers                  *int                   `json:"max_users,omitempty"`
	MaxCores                  *int                   `json:"max_cores,omitempty"`
	MaxUses                   *int                   `json:"max_uses,omitempty"`
	Scheme                    string                 `json:"scheme,omitempty"`
	HeartbeatDuration         *int                   `json:"heartbeat_duration,omitempty"`
	MachineUniquenessStrategy string                 `json:"machine_uniqueness_strategy,omitempty"`
	ExpirationStrategy        string                 `json:"expiration_strategy,omitempty"`
	OverageStrategy           string                 `json:"overage_strategy,omitempty"`
	RequireCheckIn            bool                   `json:"require_check_in"`
	CheckInInterval           *string                `json:"check_in_interval,omitempty"`
	CheckInIntervalCount      *int                   `json:"check_in_interval_count,omitempty"`
	UsePool                   bool                   `json:"use_pool"`
	MaxActivations            *int                   `json:"max_activations,omitempty"`
	MaxDeactivations          *int                   `json:"max_deactivations,omitempty"`
	Protected                 bool                   `json:"protected"`
	Metadata                  map[string]interface{} `json:"metadata,omitempty"`
	Created                   string                 `json:"created_at"`
	Updated                   string                 `json:"updated_at"`
}

type PolicyListResponse struct {
	Policies   []PolicyResponse `json:"policies"`
	Pagination PaginationInfo   `json:"pagination"`
}

// Helper function to convert entity to response
func (h *PolicyHandler) toPolicyResponse(policy *entities.Policy) PolicyResponse {
	response := PolicyResponse{
		ID:                   policy.ID,
		Name:                 policy.Name,
		Description:          policy.Description,
		ProductID:            policy.ProductID,
		EnvironmentID:        policy.EnvironmentID,
		Duration:             policy.Duration,
		Strict:               policy.Strict,
		Floating:             policy.Floating,
		RequireHeartbeat:     policy.RequireHeartbeat,
		MaxMachines:          policy.MaxMachines,
		MaxProcesses:         policy.MaxProcesses,
		MaxUsers:             policy.MaxUsers,
		MaxCores:             policy.MaxCores,
		MaxUses:              policy.MaxUses,
		HeartbeatDuration:    policy.HeartbeatDuration,
		RequireCheckIn:       policy.RequireCheckIn,
		CheckInInterval:      policy.CheckInInterval,
		CheckInIntervalCount: policy.CheckInIntervalCount,
		UsePool:              policy.UsePool,
		MaxActivations:       policy.MaxActivations,
		MaxDeactivations:     policy.MaxDeactivations,
		Metadata:             policy.Metadata,
		Created:              policy.CreatedAt.Format("2006-01-02T15:04:05Z"),
		Updated:              policy.UpdatedAt.Format("2006-01-02T15:04:05Z"),
	}

	// Handle pointer fields
	if policy.Protected != nil {
		response.Protected = *policy.Protected
	}

	// Handle enum fields
	if policy.Scheme != "" {
		response.Scheme = string(policy.Scheme)
	}
	if policy.MachineUniquenessStrategy != nil && *policy.MachineUniquenessStrategy != "" {
		response.MachineUniquenessStrategy = *policy.MachineUniquenessStrategy
	}
	if policy.ExpirationStrategy != nil && *policy.ExpirationStrategy != "" {
		response.ExpirationStrategy = *policy.ExpirationStrategy
	}
	if policy.OverageStrategy != nil && *policy.OverageStrategy != "" {
		response.OverageStrategy = *policy.OverageStrategy
	}

	return response
}

func (h *PolicyHandler) ListPoliciesHandler(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		responses.RenderUnauthorized(c, "Account ID not found in token")
		return
	}

	// Parse pagination
	page := 1
	limit := 25
	if p := c.Query("page"); p != "" {
		if parsed, err := strconv.Atoi(p); err == nil && parsed > 0 {
			page = parsed
		}
	}
	if l := c.Query("limit"); l != "" {
		if parsed, err := strconv.Atoi(l); err == nil && parsed > 0 && parsed <= 100 {
			limit = parsed
		}
	}

	// Build filter
	filter := repositories.ListFilter{
		Page:      page,
		PageSize:  limit,
		SortBy:    c.DefaultQuery("sort_by", "created_at"),
		SortOrder: c.DefaultQuery("sort_order", "DESC"),
		Search:    c.Query("search"),
		AccountID: &accountID,
		Filters:   make(map[string]interface{}),
	}

	// Add product filter if provided
	if productID := c.Query("product_id"); productID != "" {
		if pid, err := uuid.Parse(productID); err == nil {
			filter.Filters["product_id = ?"] = pid
		}
	}

	// Get policies from repository with filters
	policies, total, err := h.serviceCoordinator.Repositories.Policy().List(c.Request.Context(), filter)
	if err != nil {
		responses.RenderInternalError(c, "Failed to retrieve policies: "+err.Error())
		return
	}

	response := gin.H{
		"data": policies,
		"meta": gin.H{
			"page":        page,
			"limit":       limit,
			"total":       total,
			"total_pages": (total + int64(limit) - 1) / int64(limit),
		},
	}

	c.JSON(http.StatusOK, response)
}

func (h *PolicyHandler) GetPolicyHandler(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		responses.RenderUnauthorized(c, "Account ID not found in token")
		return
	}

	policyIDStr := c.Param("id")
	policyID, err := uuid.Parse(policyIDStr)
	if err != nil {
		responses.RenderBadRequest(c, "Invalid policy ID format")
		return
	}

	// Get policy from repository by ID
	policy, err := h.serviceCoordinator.Repositories.Policy().GetByID(c.Request.Context(), policyID)
	if err != nil {
		responses.RenderNotFound(c, "Policy not found")
		return
	}

	// Verify policy belongs to the account
	if policy.AccountID != accountID.String() {
		responses.RenderNotFound(c, "Policy not found")
		return
	}

	response := gin.H{
		"data": policy,
	}

	c.JSON(http.StatusOK, response)
}

func (h *PolicyHandler) CreatePolicyHandler(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		responses.RenderUnauthorized(c, "Account ID not found in token")
		return
	}

	var req PolicyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responses.RenderBadRequest(c, "Invalid request format: "+err.Error())
		return
	}

	// Create policy through service layer
	policy := &entities.Policy{
		AccountID:                     accountID.String(),
		Name:                          req.Data.Attributes.Name,
		Duration:                      req.Data.Attributes.Duration,
		HeartbeatDuration:             req.Data.Attributes.HeartbeatDuration,
		HeartbeatCullStrategy:         req.Data.Attributes.HeartbeatCullStrategy,
		HeartbeatResurrectionStrategy: req.Data.Attributes.HeartbeatResurrectionStrategy,
		HeartbeatBasis:                req.Data.Attributes.HeartbeatBasis,
		MachineUniquenessStrategy:     req.Data.Attributes.MachineUniquenessStrategy,
		MachineMatchingStrategy:       req.Data.Attributes.MachineMatchingStrategy,
		ComponentsStrategy:            req.Data.Attributes.ComponentsStrategy,
		ComponentsFingerprint:         req.Data.Attributes.ComponentsFingerprint,
		ExpirationStrategy:            req.Data.Attributes.ExpirationStrategy,
		ExpirationBasis:               req.Data.Attributes.ExpirationBasis,
		RenewalBasis:                  req.Data.Attributes.RenewalBasis,
		TransferStrategy:              req.Data.Attributes.TransferStrategy,
		AuthenticationStrategy:        req.Data.Attributes.AuthenticationStrategy,
		MachineLeasingStrategy:        req.Data.Attributes.MachineLeasingStrategy,
		ProcessLeasingStrategy:        req.Data.Attributes.ProcessLeasingStrategy,
		OverageStrategy:               req.Data.Attributes.OverageStrategy,
		MaxMachines:                   req.Data.Attributes.MaxMachines,
		MaxProcesses:                  req.Data.Attributes.MaxProcesses,
		MaxUsers:                      req.Data.Attributes.MaxUsers,
		MaxCores:                      req.Data.Attributes.MaxCores,
		MaxUses:                       req.Data.Attributes.MaxUses,
		MaxActivations:                req.Data.Attributes.MaxActivations,
		MaxDeactivations:              req.Data.Attributes.MaxDeactivations,
		Protected:                     req.Data.Attributes.Protected,
		CheckInInterval:               req.Data.Attributes.CheckInInterval,
		CheckInIntervalCount:          req.Data.Attributes.CheckInIntervalCount,
		Metadata:                      req.Data.Attributes.Metadata,
		CreatedAt:                     time.Now(),
		UpdatedAt:                     time.Now(),
	}

	// Set boolean fields with nil checks
	if req.Data.Attributes.Strict != nil {
		policy.Strict = *req.Data.Attributes.Strict
	}
	if req.Data.Attributes.Floating != nil {
		policy.Floating = *req.Data.Attributes.Floating
	}
	if req.Data.Attributes.RequireHeartbeat != nil {
		policy.RequireHeartbeat = *req.Data.Attributes.RequireHeartbeat
	}
	if req.Data.Attributes.RequireCheckIn != nil {
		policy.RequireCheckIn = *req.Data.Attributes.RequireCheckIn
	}
	if req.Data.Attributes.UsePool != nil {
		policy.UsePool = *req.Data.Attributes.UsePool
	}

	// Set scheme with type conversion
	if req.Data.Attributes.Scheme != nil {
		policy.Scheme = entities.LicenseScheme(*req.Data.Attributes.Scheme)
	}

	// Set product ID if provided
	if req.Data.Relationships != nil && req.Data.Relationships.Product != nil {
		policy.ProductID = req.Data.Relationships.Product.Data.ID
	}

	// Set environment ID if provided
	if req.Data.Relationships != nil && req.Data.Relationships.Environment != nil {
		policy.EnvironmentID = &req.Data.Relationships.Environment.Data.ID
	}

	// Save policy to repository
	if err := h.serviceCoordinator.Repositories.Policy().Create(c.Request.Context(), policy); err != nil {
		responses.RenderInternalError(c, "Failed to create policy: "+err.Error())
		return
	}

	// Broadcast event
	account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	h.serviceCoordinator.Events.BroadcastEvent(
		c.Request.Context(),
		events.EventPolicyCreated,
		account,
		events.MakeEventResource(policy),
		events.EventMeta{},
	)

	response := gin.H{
		"data": policy,
	}

	c.Header("Location", "/api/v1/policies/"+policy.ID)
	c.JSON(http.StatusCreated, response)
}

func (h *PolicyHandler) UpdatePolicyHandler(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		responses.RenderUnauthorized(c, "Account ID not found in token")
		return
	}

	policyIDStr := c.Param("id")
	policyID, err := uuid.Parse(policyIDStr)
	if err != nil {
		responses.RenderBadRequest(c, "Invalid policy ID format")
		return
	}

	var req PolicyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responses.RenderBadRequest(c, "Invalid request format: "+err.Error())
		return
	}

	// Get existing policy
	policy, err := h.serviceCoordinator.Repositories.Policy().GetByID(c.Request.Context(), policyID)
	if err != nil {
		responses.RenderNotFound(c, "Policy not found")
		return
	}

	// Verify policy belongs to the account
	if policy.AccountID != accountID.String() {
		responses.RenderNotFound(c, "Policy not found")
		return
	}

	// Update policy fields
	policy.Name = req.Data.Attributes.Name
	if req.Data.Attributes.Duration != nil {
		policy.Duration = req.Data.Attributes.Duration
	}
	if req.Data.Attributes.Strict != nil {
		policy.Strict = *req.Data.Attributes.Strict
	}
	if req.Data.Attributes.Floating != nil {
		policy.Floating = *req.Data.Attributes.Floating
	}
	if req.Data.Attributes.Scheme != nil {
		policy.Scheme = entities.LicenseScheme(*req.Data.Attributes.Scheme)
	}
	if req.Data.Attributes.RequireHeartbeat != nil {
		policy.RequireHeartbeat = *req.Data.Attributes.RequireHeartbeat
	}
	if req.Data.Attributes.HeartbeatDuration != nil {
		policy.HeartbeatDuration = req.Data.Attributes.HeartbeatDuration
	}
	if req.Data.Attributes.RequireCheckIn != nil {
		policy.RequireCheckIn = *req.Data.Attributes.RequireCheckIn
	}
	if req.Data.Attributes.UsePool != nil {
		policy.UsePool = *req.Data.Attributes.UsePool
	}
	if req.Data.Attributes.MaxMachines != nil {
		policy.MaxMachines = req.Data.Attributes.MaxMachines
	}
	if req.Data.Attributes.MaxProcesses != nil {
		policy.MaxProcesses = req.Data.Attributes.MaxProcesses
	}
	if req.Data.Attributes.MaxUsers != nil {
		policy.MaxUsers = req.Data.Attributes.MaxUsers
	}
	if req.Data.Attributes.MaxCores != nil {
		policy.MaxCores = req.Data.Attributes.MaxCores
	}
	if req.Data.Attributes.MaxUses != nil {
		policy.MaxUses = req.Data.Attributes.MaxUses
	}
	if req.Data.Attributes.OverageStrategy != nil {
		policy.OverageStrategy = req.Data.Attributes.OverageStrategy
	}
	if req.Data.Attributes.Metadata != nil {
		policy.Metadata = req.Data.Attributes.Metadata
	}
	policy.UpdatedAt = time.Now()

	// Save to repository
	if err := h.serviceCoordinator.Repositories.Policy().Update(c.Request.Context(), policy); err != nil {
		responses.RenderInternalError(c, "Failed to update policy: "+err.Error())
		return
	}

	// Broadcast event
	account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	h.serviceCoordinator.Events.BroadcastEvent(
		c.Request.Context(),
		events.EventPolicyUpdated,
		account,
		events.MakeEventResource(policy),
		events.EventMeta{},
	)

	response := gin.H{
		"data": policy,
	}

	c.JSON(http.StatusOK, response)
}

func (h *PolicyHandler) DeletePolicyHandler(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		responses.RenderUnauthorized(c, "Account ID not found in token")
		return
	}

	policyIDStr := c.Param("id")
	policyID, err := uuid.Parse(policyIDStr)
	if err != nil {
		responses.RenderBadRequest(c, "Invalid policy ID format")
		return
	}

	// Get policy from repository
	policy, err := h.serviceCoordinator.Repositories.Policy().GetByID(c.Request.Context(), policyID)
	if err != nil {
		responses.RenderNotFound(c, "Policy not found")
		return
	}

	// Verify policy belongs to the account
	if policy.AccountID != accountID.String() {
		responses.RenderNotFound(c, "Policy not found")
		return
	}

	// Delete policy
	if err := h.serviceCoordinator.Repositories.Policy().Delete(c.Request.Context(), policyID); err != nil {
		responses.RenderInternalError(c, "Failed to delete policy: "+err.Error())
		return
	}

	// Broadcast event
	account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	h.serviceCoordinator.Events.BroadcastEvent(
		c.Request.Context(),
		events.EventPolicyDeleted,
		account,
		events.MakeEventResource(policy),
		events.EventMeta{},
	)

	c.Status(http.StatusNoContent)
}
