package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/adapters/http/middleware"
	"github.com/gokeys/gokeys/internal/adapters/http/responses"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/gokeys/gokeys/internal/domain/services"
	"github.com/gokeys/gokeys/internal/domain/services/events"
	"github.com/google/uuid"
)

type GroupHandler struct {
	serviceCoordinator *services.ServiceCoordinator
}

func NewGroupHandler(serviceCoordinator *services.ServiceCoordinator) *GroupHandler {
	return &GroupHandler{
		serviceCoordinator: serviceCoordinator,
	}
}

type GroupRequest struct {
	Data struct {
		Type       string `json:"type" binding:"required"`
		Attributes struct {
			Name        string                 `json:"name" binding:"required"`
			MaxUsers    *int                   `json:"max_users,omitempty"`
			MaxLicenses *int                   `json:"max_licenses,omitempty"`
			MaxMachines *int                   `json:"max_machines,omitempty"`
			Metadata    map[string]interface{} `json:"metadata,omitempty"`
		} `json:"attributes"`
		Relationships *struct {
			Environment *struct {
				Data *struct {
					Type string `json:"type"`
					ID   string `json:"id"`
				} `json:"data"`
			} `json:"environment,omitempty"`
		} `json:"relationships,omitempty"`
	} `json:"data"`
}

func (h *GroupHandler) ListGroupsHandler(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		responses.RenderUnauthorized(c, "Account ID not found in token")
		return
	}

	// Parse pagination
	page := 1
	limit := 25
	if p := c.Query("page"); p != "" {
		if parsed, err := strconv.Atoi(p); err == nil && parsed > 0 {
			page = parsed
		}
	}
	if l := c.Query("limit"); l != "" {
		if parsed, err := strconv.Atoi(l); err == nil && parsed > 0 && parsed <= 100 {
			limit = parsed
		}
	}

	// Build filter
	filter := repositories.ListFilter{
		Page:      page,
		PageSize:  limit,
		SortBy:    c.DefaultQuery("sort_by", "created_at"),
		SortOrder: c.DefaultQuery("sort_order", "DESC"),
		Search:    c.Query("search"),
		AccountID: &accountID,
		Filters:   make(map[string]interface{}),
	}

	// Get groups from repository
	groups, total, err := h.serviceCoordinator.Repositories.Group().List(c.Request.Context(), filter)
	if err != nil {
		responses.RenderInternalError(c, "Failed to retrieve groups: "+err.Error())
		return
	}

	response := gin.H{
		"data": groups,
		"meta": gin.H{
			"page":        page,
			"limit":       limit,
			"total":       total,
			"total_pages": (total + int64(limit) - 1) / int64(limit),
		},
	}

	c.JSON(http.StatusOK, response)
}

func (h *GroupHandler) GetGroupHandler(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		responses.RenderUnauthorized(c, "Account ID not found in token")
		return
	}

	groupIDStr := c.Param("id")
	groupID, err := uuid.Parse(groupIDStr)
	if err != nil {
		responses.RenderBadRequest(c, "Invalid group ID format")
		return
	}

	// Get group from repository by ID
	group, err := h.serviceCoordinator.Repositories.Group().GetByID(c.Request.Context(), groupID)
	if err != nil {
		responses.RenderNotFound(c, "Group not found")
		return
	}

	// Verify group belongs to the account
	if group.AccountID != accountID.String() {
		responses.RenderNotFound(c, "Group not found")
		return
	}

	response := gin.H{
		"data": group,
	}

	c.JSON(http.StatusOK, response)
}

func (h *GroupHandler) CreateGroupHandler(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		responses.RenderUnauthorized(c, "Account ID not found in token")
		return
	}

	var req GroupRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responses.RenderBadRequest(c, "Invalid request format: "+err.Error())
		return
	}

	// Create group through service layer
	group := &entities.Group{
		AccountID:   accountID.String(),
		Name:        req.Data.Attributes.Name,
		MaxUsers:    req.Data.Attributes.MaxUsers,
		MaxLicenses: req.Data.Attributes.MaxLicenses,
		MaxMachines: req.Data.Attributes.MaxMachines,
		Metadata:    req.Data.Attributes.Metadata,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// Set environment ID if provided
	if req.Data.Relationships != nil && req.Data.Relationships.Environment != nil {
		group.EnvironmentID = &req.Data.Relationships.Environment.Data.ID
	}

	// Save group to repository
	if err := h.serviceCoordinator.Repositories.Group().Create(c.Request.Context(), group); err != nil {
		responses.RenderInternalError(c, "Failed to create group: "+err.Error())
		return
	}

	// Broadcast event
	account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	h.serviceCoordinator.Events.BroadcastEvent(
		c.Request.Context(),
		events.EventGroupCreated,
		account,
		events.MakeEventResource(group),
		events.EventMeta{},
	)

	response := gin.H{
		"data": group,
	}

	c.Header("Location", "/api/v1/groups/"+group.ID)
	c.JSON(http.StatusCreated, response)
}

func (h *GroupHandler) UpdateGroupHandler(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		responses.RenderUnauthorized(c, "Account ID not found in token")
		return
	}

	groupIDStr := c.Param("id")
	groupID, err := uuid.Parse(groupIDStr)
	if err != nil {
		responses.RenderBadRequest(c, "Invalid group ID format")
		return
	}

	var req GroupRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responses.RenderBadRequest(c, "Invalid request format: "+err.Error())
		return
	}

	// Get existing group
	group, err := h.serviceCoordinator.Repositories.Group().GetByID(c.Request.Context(), groupID)
	if err != nil {
		responses.RenderNotFound(c, "Group not found")
		return
	}

	// Verify group belongs to the account
	if group.AccountID != accountID.String() {
		responses.RenderNotFound(c, "Group not found")
		return
	}

	// Update group fields
	group.Name = req.Data.Attributes.Name
	if req.Data.Attributes.MaxUsers != nil {
		group.MaxUsers = req.Data.Attributes.MaxUsers
	}
	if req.Data.Attributes.MaxLicenses != nil {
		group.MaxLicenses = req.Data.Attributes.MaxLicenses
	}
	if req.Data.Attributes.MaxMachines != nil {
		group.MaxMachines = req.Data.Attributes.MaxMachines
	}
	if req.Data.Attributes.Metadata != nil {
		group.Metadata = req.Data.Attributes.Metadata
	}
	group.UpdatedAt = time.Now()

	// Save to repository
	if err := h.serviceCoordinator.Repositories.Group().Update(c.Request.Context(), group); err != nil {
		responses.RenderInternalError(c, "Failed to update group: "+err.Error())
		return
	}

	// Broadcast event
	account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	h.serviceCoordinator.Events.BroadcastEvent(
		c.Request.Context(),
		events.EventGroupUpdated,
		account,
		events.MakeEventResource(group),
		events.EventMeta{},
	)

	response := gin.H{
		"data": group,
	}

	c.JSON(http.StatusOK, response)
}

func (h *GroupHandler) DeleteGroupHandler(c *gin.Context) {
	accountID, err := middleware.GetAccountID(c)
	if err != nil {
		responses.RenderUnauthorized(c, "Account ID not found in token")
		return
	}

	groupIDStr := c.Param("id")
	groupID, err := uuid.Parse(groupIDStr)
	if err != nil {
		responses.RenderBadRequest(c, "Invalid group ID format")
		return
	}

	// Get group from repository
	group, err := h.serviceCoordinator.Repositories.Group().GetByID(c.Request.Context(), groupID)
	if err != nil {
		responses.RenderNotFound(c, "Group not found")
		return
	}

	// Verify group belongs to the account
	if group.AccountID != accountID.String() {
		responses.RenderNotFound(c, "Group not found")
		return
	}

	// Delete group
	if err := h.serviceCoordinator.Repositories.Group().Delete(c.Request.Context(), groupID); err != nil {
		responses.RenderInternalError(c, "Failed to delete group: "+err.Error())
		return
	}

	// Broadcast event
	account, _ := h.serviceCoordinator.Repositories.Account().GetByID(c.Request.Context(), accountID)
	h.serviceCoordinator.Events.BroadcastEvent(
		c.Request.Context(),
		events.EventGroupDeleted,
		account,
		events.MakeEventResource(group),
		events.EventMeta{},
	)

	c.Status(http.StatusNoContent)
}