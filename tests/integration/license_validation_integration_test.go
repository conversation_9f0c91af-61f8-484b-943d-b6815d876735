package integration

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/rs/zerolog"

	"github.com/gokeys/gokeys/internal/adapters/database"
	"github.com/gokeys/gokeys/internal/adapters/http/handlers"
	"github.com/gokeys/gokeys/internal/config"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/services"
)

// TestLicenseValidationIntegration tests the complete license validation workflow
// using real ServiceCoordinator, repositories, and database operations
func TestLicenseValidationIntegration(t *testing.T) {
	// Skip if not in integration test mode
	if testing.Short() {
		t.Skip("Skipping integration tests in short mode")
	}

	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Create test config
	cfg := &config.Config{
		Database: config.DatabaseConfig{
			Host:            "localhost",
			Port:            5432,
			User:            "gokeys",
			Password:        "gokeys",
			DBName:          "gokeys_test",
			SSLMode:         "disable",
			MaxIdleConns:    10,
			MaxOpenConns:    100,
			ConnMaxLifetime: 3600,
		},
	}

	// Initialize database
	dbService, err := database.NewPostgreSQL(database.Config{
		Host:            cfg.Database.Host,
		Port:            cfg.Database.Port,
		User:            cfg.Database.User,
		Password:        cfg.Database.Password,
		DBName:          cfg.Database.DBName,
		SSLMode:         cfg.Database.SSLMode,
		MaxIdleConns:    cfg.Database.MaxIdleConns,
		MaxOpenConns:    cfg.Database.MaxOpenConns,
		ConnMaxLifetime: cfg.Database.ConnMaxLifetime,
	})
	if err != nil {
		t.Fatalf("Failed to initialize database: %v", err)
	}
	defer dbService.Close()

	// Initialize database schema
	err = dbService.Initialize()
	if err != nil {
		t.Fatalf("Failed to initialize database schema: %v", err)
	}

	// Initialize logger
	logger := zerolog.New(os.Stdout).With().
		Timestamp().
		Str("service", "gokeys-integration-test").
		Logger()

	// Initialize service coordinator with all real dependencies
	serviceCoordinator := services.NewServiceCoordinator(dbService.GetDB(), logger)

	// Setup test data using real repositories
	ctx := context.Background()
	testData := setupTestData(t, ctx, serviceCoordinator)
	defer cleanupTestData(ctx, serviceCoordinator, testData)

	// Test the complete workflow
	t.Run("CompleteValidationWorkflow", func(t *testing.T) {
		testCompleteValidationWorkflow(t, serviceCoordinator, testData)
	})

	t.Run("CacheIntegration", func(t *testing.T) {
		testCacheIntegration(t, serviceCoordinator, testData)
	})

	t.Run("ErrorHandling", func(t *testing.T) {
		testErrorHandling(t, serviceCoordinator)
	})

	t.Run("MachineRegistration", func(t *testing.T) {
		testMachineRegistration(t, serviceCoordinator, testData)
	})

	t.Run("ServiceHealth", func(t *testing.T) {
		testServiceHealth(t, serviceCoordinator)
	})
}

// TestData holds test entities
type TestData struct {
	AccountID      string
	ProductID      string
	PolicyID       string
	LicenseID      string
	LicenseKey     string
}

// setupTestData creates test entities using real repositories
func setupTestData(t *testing.T, ctx context.Context, serviceCoordinator *services.ServiceCoordinator) *TestData {
	// Create account
	privateKey := "test-private-key"
	publicKey := "test-public-key"
	testAccount := &entities.Account{
		ID:         uuid.New().String(),
		Name:       "Integration Test Account",
		Slug:       "integration-test-account",
		Email:      "<EMAIL>",
		PrivateKey: &privateKey,
		PublicKey:  &publicKey,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}

	err := serviceCoordinator.Repositories.Account().Create(ctx, testAccount)
	if err != nil {
		t.Fatalf("Failed to create test account: %v", err)
	}

	// Create product
	testProduct := &entities.Product{
		ID:        uuid.New().String(),
		AccountID: testAccount.ID,
		Name:      "Integration Test Product",
		Code:      "integration-test-product",
		Key:       "integration-test-product-key",
		URL:       "https://example.com/integration-test",
		Platforms: entities.ProductPlatforms{
			Supported: []string{"linux", "windows", "macos"},
		},
		Metadata:  map[string]interface{}{"test": "integration"},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	err = serviceCoordinator.Repositories.Product().Create(ctx, testProduct)
	if err != nil {
		t.Fatalf("Failed to create test product: %v", err)
	}

	// Create policy
	duration := ******** // 1 year
	heartbeatDuration := 3600
	testPolicy := &entities.Policy{
		ID:                uuid.New().String(),
		AccountID:         testAccount.ID,
		ProductID:         testProduct.ID,
		Name:              "Integration Test Policy",
		Duration:          &duration,
		Strict:            false,
		Floating:          true,
		RequireHeartbeat:  false,
		HeartbeatDuration: &heartbeatDuration,
		Metadata:          map[string]interface{}{"test": "integration"},
		CreatedAt:         time.Now(),
		UpdatedAt:         time.Now(),
	}

	err = serviceCoordinator.Repositories.Policy().Create(ctx, testPolicy)
	if err != nil {
		t.Fatalf("Failed to create test policy: %v", err)
	}

	// Create license
	testLicenseKey := "LIC-INTEGRATION-" + uuid.New().String()[:8]
	testLicense := &entities.License{
		ID:        uuid.New().String(),
		AccountID: testAccount.ID,
		ProductID: testProduct.ID,
		PolicyID:  testPolicy.ID,
		Key:       testLicenseKey,
		Name:      "Integration Test License",
		Status:    entities.LicenseStatusActive,
		Metadata:  map[string]interface{}{"test": "integration"},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	err = serviceCoordinator.Repositories.License().Create(ctx, testLicense)
	if err != nil {
		t.Fatalf("Failed to create test license: %v", err)
	}

	return &TestData{
		AccountID:  testAccount.ID,
		ProductID:  testProduct.ID,
		PolicyID:   testPolicy.ID,
		LicenseID:  testLicense.ID,
		LicenseKey: testLicenseKey,
	}
}

// cleanupTestData removes test entities
func cleanupTestData(ctx context.Context, serviceCoordinator *services.ServiceCoordinator, testData *TestData) {
	// Delete in reverse order due to foreign key constraints
	if testData.LicenseID != "" {
		licenseUUID, _ := uuid.Parse(testData.LicenseID)
		serviceCoordinator.Repositories.License().Delete(ctx, licenseUUID)
	}

	if testData.PolicyID != "" {
		policyUUID, _ := uuid.Parse(testData.PolicyID)
		serviceCoordinator.Repositories.Policy().Delete(ctx, policyUUID)
	}

	if testData.ProductID != "" {
		productUUID, _ := uuid.Parse(testData.ProductID)
		serviceCoordinator.Repositories.Product().Delete(ctx, productUUID)
	}

	if testData.AccountID != "" {
		accountUUID, _ := uuid.Parse(testData.AccountID)
		serviceCoordinator.Repositories.Account().Delete(ctx, accountUUID)
	}
}

// testCompleteValidationWorkflow tests the entire validation workflow
func testCompleteValidationWorkflow(t *testing.T, serviceCoordinator *services.ServiceCoordinator, testData *TestData) {
	// Test POST validation
	machineFingerprint := "integration-test-machine-" + uuid.New().String()[:8]
	requestBody := handlers.ValidateLicenseRequest{
		LicenseKey:         testData.LicenseKey,
		MachineFingerprint: &machineFingerprint,
		Environment:        stringPtr("integration-test"),
		MachineInfo: map[string]interface{}{
			"hostname": "integration-test-machine",
			"os":       "linux",
			"arch":     "x86_64",
			"memory":   "16GB",
		},
	}

	bodyBytes, err := json.Marshal(requestBody)
	if err != nil {
		t.Fatalf("Failed to marshal request: %v", err)
	}

	// Create HTTP request and test context
	req := httptest.NewRequest("POST", "/api/v1/licenses/validate", bytes.NewBuffer(bodyBytes))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// Execute handler using real ServiceCoordinator
	handler := handlers.NewLicenseHandler(serviceCoordinator)
	handler.ValidatePostHandler(c)

	// Verify HTTP response
	if w.Code != http.StatusOK {
		t.Errorf("Expected status 200, got %d. Body: %s", w.Code, w.Body.String())
		return
	}

	var response handlers.ValidateLicenseResponse
	err = json.Unmarshal(w.Body.Bytes(), &response)
	if err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}

	// Verify validation response
	if !response.Valid {
		t.Errorf("Expected license to be valid, but got invalid. Errors: %v", response.Errors)
	}
	if response.ValidationTime.IsZero() {
		t.Error("Expected non-zero validation time")
	}
	if len(response.Errors) > 0 {
		t.Errorf("Expected no errors, got %v", response.Errors)
	}

	// Test GET validation
	getReq := httptest.NewRequest("GET", fmt.Sprintf("/api/v1/licenses/validate?license_key=%s&machine_fingerprint=%s&environment=test", testData.LicenseKey, machineFingerprint), nil)
	getW := httptest.NewRecorder()
	getC, _ := gin.CreateTestContext(getW)
	getC.Request = getReq

	handler.ValidateGetHandler(getC)

	if getW.Code != http.StatusOK {
		t.Errorf("Expected status 200 for GET validation, got %d", getW.Code)
	}

	// Test quick validation
	quickReq := httptest.NewRequest("GET", fmt.Sprintf("/api/v1/licenses/quick-validate?license_key=%s", testData.LicenseKey), nil)
	quickW := httptest.NewRecorder()
	quickC, _ := gin.CreateTestContext(quickW)
	quickC.Request = quickReq

	handler.QuickValidateHandler(quickC)

	if quickW.Code != http.StatusOK {
		t.Errorf("Expected status 200 for quick validation, got %d", quickW.Code)
	}

	var quickResponse map[string]interface{}
	err = json.Unmarshal(quickW.Body.Bytes(), &quickResponse)
	if err != nil {
		t.Fatalf("Failed to unmarshal quick response: %v", err)
	}

	valid, ok := quickResponse["valid"].(bool)
	if !ok || !valid {
		t.Error("Expected valid license in quick validation")
	}

	// Test license info
	infoReq := httptest.NewRequest("GET", fmt.Sprintf("/api/v1/licenses/info?license_key=%s", testData.LicenseKey), nil)
	infoW := httptest.NewRecorder()
	infoC, _ := gin.CreateTestContext(infoW)
	infoC.Request = infoReq

	handler.InfoHandler(infoC)

	if infoW.Code != http.StatusOK {
		t.Errorf("Expected status 200 for license info, got %d", infoW.Code)
	}
}

// testCacheIntegration tests caching behavior
func testCacheIntegration(t *testing.T, serviceCoordinator *services.ServiceCoordinator, testData *TestData) {
	requestBody := handlers.ValidateLicenseRequest{
		LicenseKey:         testData.LicenseKey,
		MachineFingerprint: stringPtr("cache-test-machine"),
		Environment:        stringPtr("cache-test"),
	}

	bodyBytes, err := json.Marshal(requestBody)
	if err != nil {
		t.Fatalf("Failed to marshal request: %v", err)
	}

	handler := handlers.NewLicenseHandler(serviceCoordinator)

	// First request - should hit database
	req1 := httptest.NewRequest("POST", "/api/v1/licenses/validate", bytes.NewBuffer(bodyBytes))
	req1.Header.Set("Content-Type", "application/json")
	w1 := httptest.NewRecorder()
	c1, _ := gin.CreateTestContext(w1)
	c1.Request = req1

	start1 := time.Now()
	handler.ValidatePostHandler(c1)
	duration1 := time.Since(start1)

	if w1.Code != http.StatusOK {
		t.Errorf("Expected status 200 for first request, got %d", w1.Code)
		return
	}

	// Second request - should hit cache
	req2 := httptest.NewRequest("POST", "/api/v1/licenses/validate", bytes.NewBuffer(bodyBytes))
	req2.Header.Set("Content-Type", "application/json")
	w2 := httptest.NewRecorder()
	c2, _ := gin.CreateTestContext(w2)
	c2.Request = req2

	start2 := time.Now()
	handler.ValidatePostHandler(c2)
	duration2 := time.Since(start2)

	if w2.Code != http.StatusOK {
		t.Errorf("Expected status 200 for second request, got %d", w2.Code)
		return
	}

	t.Logf("First request: %v, Second request: %v", duration1, duration2)

	// Test cache invalidation
	invalidateReq := httptest.NewRequest("POST", fmt.Sprintf("/api/v1/licenses/invalidate-cache?license_key=%s", testData.LicenseKey), nil)
	invalidateW := httptest.NewRecorder()
	invalidateC, _ := gin.CreateTestContext(invalidateW)
	invalidateC.Request = invalidateReq

	handler.InvalidateCacheHandler(invalidateC)

	if invalidateW.Code != http.StatusOK {
		t.Errorf("Expected status 200 for cache invalidation, got %d", invalidateW.Code)
	}
}

// testErrorHandling tests error scenarios
func testErrorHandling(t *testing.T, serviceCoordinator *services.ServiceCoordinator) {
	handler := handlers.NewLicenseHandler(serviceCoordinator)

	// Test invalid license key
	requestBody := handlers.ValidateLicenseRequest{
		LicenseKey: "INVALID-LICENSE-KEY-123",
	}

	bodyBytes, err := json.Marshal(requestBody)
	if err != nil {
		t.Fatalf("Failed to marshal request: %v", err)
	}

	req := httptest.NewRequest("POST", "/api/v1/licenses/validate", bytes.NewBuffer(bodyBytes))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	handler.ValidatePostHandler(c)

	// Should return validation failure
	if w.Code != http.StatusForbidden {
		t.Errorf("Expected status 403 for invalid license, got %d", w.Code)
	}

	var response handlers.ValidateLicenseResponse
	err = json.Unmarshal(w.Body.Bytes(), &response)
	if err != nil {
		t.Fatalf("Failed to unmarshal error response: %v", err)
	}

	if response.Valid {
		t.Error("Expected invalid license")
	}
	if len(response.Errors) == 0 {
		t.Error("Expected errors for invalid license")
	}

	// Test malformed request
	malformedReq := httptest.NewRequest("POST", "/api/v1/licenses/validate", bytes.NewBufferString(`{"invalid": json`))
	malformedReq.Header.Set("Content-Type", "application/json")
	malformedW := httptest.NewRecorder()
	malformedC, _ := gin.CreateTestContext(malformedW)
	malformedC.Request = malformedReq

	handler.ValidatePostHandler(malformedC)

	if malformedW.Code != http.StatusBadRequest {
		t.Errorf("Expected status 400 for malformed request, got %d", malformedW.Code)
	}
}

// testMachineRegistration tests machine registration through validation
func testMachineRegistration(t *testing.T, serviceCoordinator *services.ServiceCoordinator, testData *TestData) {
	ctx := context.Background()
	machineFingerprint := "machine-registration-test-" + uuid.New().String()[:8]

	requestBody := handlers.ValidateLicenseRequest{
		LicenseKey:         testData.LicenseKey,
		MachineFingerprint: &machineFingerprint,
		Environment:        stringPtr("machine-test"),
		MachineInfo: map[string]interface{}{
			"hostname": "machine-registration-test",
			"os":       "linux",
			"arch":     "x86_64",
		},
	}

	bodyBytes, err := json.Marshal(requestBody)
	if err != nil {
		t.Fatalf("Failed to marshal request: %v", err)
	}

	req := httptest.NewRequest("POST", "/api/v1/licenses/validate", bytes.NewBuffer(bodyBytes))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	handler := handlers.NewLicenseHandler(serviceCoordinator)
	handler.ValidatePostHandler(c)

	if w.Code != http.StatusOK {
		t.Errorf("Expected status 200, got %d", w.Code)
		return
	}

	// Verify machine was registered using real repository
	accountUUID, _ := uuid.Parse(testData.AccountID)
	machine, err := serviceCoordinator.Repositories.Machine().GetByFingerprint(ctx, machineFingerprint, accountUUID)
	if err != nil {
		t.Fatalf("Failed to get machine by fingerprint: %v", err)
	}

	if machine == nil {
		t.Fatal("Expected machine to be registered, but got nil")
	}

	// Verify machine properties
	if machine.AccountID != testData.AccountID {
		t.Errorf("Expected machine account ID %s, got %s", testData.AccountID, machine.AccountID)
	}
	if machine.LicenseID != testData.LicenseID {
		t.Errorf("Expected machine license ID %s, got %s", testData.LicenseID, machine.LicenseID)
	}
	if machine.Fingerprint != machineFingerprint {
		t.Errorf("Expected machine fingerprint %s, got %s", machineFingerprint, machine.Fingerprint)
	}
	if machine.Hostname != "machine-registration-test" {
		t.Errorf("Expected machine hostname %s, got %s", "machine-registration-test", machine.Hostname)
	}

	// Clean up machine
	machineUUID, _ := uuid.Parse(machine.ID)
	serviceCoordinator.Repositories.Machine().Delete(ctx, machineUUID)
}

// testServiceHealth tests service coordinator health
func testServiceHealth(t *testing.T, serviceCoordinator *services.ServiceCoordinator) {
	health := serviceCoordinator.Health()

	// Verify all services are healthy
	expectedServices := []string{"crypto", "cache", "database"}
	for _, serviceName := range expectedServices {
		serviceHealth, ok := health[serviceName].(map[string]interface{})
		if !ok {
			t.Errorf("Expected %s service health info", serviceName)
			continue
		}

		status, ok := serviceHealth["status"].(string)
		if !ok || status != "healthy" {
			t.Errorf("Expected %s service to be healthy, got %v", serviceName, status)
		}
	}

	// Verify crypto schemes
	cryptoHealth := health["crypto"].(map[string]interface{})
	schemes, ok := cryptoHealth["supported_schemes"].([]string)
	if !ok {
		t.Error("Expected supported crypto schemes")
	} else {
		foundRSA := false
		for _, scheme := range schemes {
			if scheme == "rsa" {
				foundRSA = true
				break
			}
		}
		if !foundRSA {
			t.Error("Expected RSA scheme to be supported")
		}
	}

	// Test service info
	info := serviceCoordinator.GetServiceInfo()
	
	services := info["services"].([]string)
	expectedInfoServices := []string{"crypto", "license_validation", "repositories", "cache"}
	
	for _, expected := range expectedInfoServices {
		found := false
		for _, service := range services {
			if service == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected service %s to be present in service info", expected)
		}
	}

	// Verify cache type
	cacheType := info["cache_type"].(string)
	if cacheType != "memory" && cacheType != "valkey" {
		t.Errorf("Expected cache type to be 'memory' or 'valkey', got %s", cacheType)
	}
}

// Helper function
func stringPtr(s string) *string {
	return &s
}