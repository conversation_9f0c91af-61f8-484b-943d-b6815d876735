{"permissions": {"allow": ["Bash(go mod init:*)", "<PERSON><PERSON>(go:*)", "Bash(ls:*)", "Bash(export:*)", "Bash(export PATH=$GOROOT/bin:$PATH)", "<PERSON><PERSON>(mkdir:*)", "Bash(rm:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(chmod:*)", "Bash(make deploy-dev:*)", "<PERSON><PERSON>(docker-compose:*)", "Bash(docker logs:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(docker exec:*)", "<PERSON><PERSON>(docker restart:*)", "<PERSON><PERSON>(source:*)", "Bash(./run-docker-local.sh:*)", "Bash(AUTO_MIGRATE=false DATABASE_HOST=localhost VALKEY_HOST=localhost go run cmd/server/main.go)", "<PERSON><PERSON>(pkill:*)", "WebFetch(domain:github.com)", "Bash(find:*)", "mcp__ide__getDiagnostics", "Bash(grep:*)", "<PERSON><PERSON>(sed:*)", "Bash(git checkout:*)", "<PERSON><PERSON>(touch:*)", "Bash(./scripts/run-integration-tests.sh:*)", "Bash(./migrate-tool)", "<PERSON><PERSON>(cat:*)"], "deny": []}}