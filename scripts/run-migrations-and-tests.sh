#!/bin/bash

# Script to run migrations and then run tests with real database
# This demonstrates how to use the migration system before running tests

set -e

echo "🚀 GoKeys Migration and Test Runner"
echo "=================================="

# Check if database is running
echo "📡 Checking database connection..."
if ! nc -z localhost 5432; then
    echo "❌ PostgreSQL is not running on localhost:5432"
    echo "Please start PostgreSQL using Docker:"
    echo "  docker run -d --name gokeys-postgres \\"
    echo "    -p 5432:5432 \\"
    echo "    -e POSTGRES_USER=gokeys \\"
    echo "    -e POSTGRES_PASSWORD=gokeys_dev_password \\"
    echo "    -e POSTGRES_DB=gokeys_test \\"
    echo "    postgres:15"
    exit 1
fi

echo "✅ Database is running"

# Run migrations
echo ""
echo "🔄 Running database migrations..."
echo "================================"

# Create extensions first
echo "Creating PostgreSQL extensions..."
go run cmd/migrate/main.go -extensions

# Run all migrations
echo "Running migrations..."
go run cmd/migrate/main.go -up

# Check migration status
echo "Checking migration status..."
go run cmd/migrate/main.go -status

echo "✅ Migrations completed successfully"

# Run tests
echo ""
echo "🧪 Running integration tests..."
echo "=============================="

# Run specific test handlers that use real database
echo "Running license handler tests..."
go test ./internal/adapters/http/handlers -run TestLicenseHandlerRealWorkflow -v

echo "Running license integration tests..."
go test ./internal/adapters/http/handlers -run TestLicenseHandlerProductionIntegration -v

echo "Running all license tests..."
go test ./internal/adapters/http/handlers -run TestLicense -v

echo ""
echo "✅ All tests completed!"
echo ""
echo "📋 Summary:"
echo "- Migrations: ✅ Applied successfully"
echo "- Tests: ✅ Passed with real database"
echo "- Database: Real PostgreSQL with full schema"
echo ""
echo "🎉 Ready for production-like testing!"