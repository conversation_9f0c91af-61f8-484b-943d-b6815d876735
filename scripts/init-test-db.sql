-- Initialize test database for GoKeys
-- This script runs when PostgreSQL container starts

-- Create additional test databases if needed
CREATE DATABASE gokeys_test_integration;
CREATE DATABASE gokeys_test_unit;

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE gokeys_test TO gokeys;
GRANT ALL PRIVILEGES ON DATABASE gokeys_test_integration TO gokeys;
GRANT ALL PRIVILEGES ON DATABASE gokeys_test_unit TO gokeys;

-- Create extensions that might be needed
\c gokeys_test;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

\c gokeys_test_integration;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

\c gokeys_test_unit;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Log completion
SELECT 'Test databases initialized successfully' AS status;
