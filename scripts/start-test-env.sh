#!/bin/bash

# Start Test Environment Script
# This script starts PostgreSQL and Valkey for testing

set -e

echo "🚀 Starting GoKeys Test Environment..."
echo ""

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose is not installed. Please install docker-compose first."
    exit 1
fi

# Start services
echo "📦 Starting PostgreSQL and Valkey containers..."
docker-compose -f docker-compose.test.yml up -d

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 5

# Check PostgreSQL
echo "🔍 Checking PostgreSQL connection..."
for i in {1..30}; do
    if docker-compose -f docker-compose.test.yml exec -T postgres-test pg_isready -U gokeys -d gokeys_test > /dev/null 2>&1; then
        echo "✅ PostgreSQL is ready!"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ PostgreSQL failed to start after 30 attempts"
        docker-compose -f docker-compose.test.yml logs postgres-test
        exit 1
    fi
    echo "   Attempt $i/30: PostgreSQL not ready yet..."
    sleep 1
done

# Check Valkey
echo "🔍 Checking Valkey connection..."
for i in {1..30}; do
    if docker-compose -f docker-compose.test.yml exec -T valkey-test valkey-cli ping > /dev/null 2>&1; then
        echo "✅ Valkey is ready!"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ Valkey failed to start after 30 attempts"
        docker-compose -f docker-compose.test.yml logs valkey-test
        exit 1
    fi
    echo "   Attempt $i/30: Valkey not ready yet..."
    sleep 1
done

echo ""
echo "🎉 Test environment is ready!"
echo ""
echo "📊 Service Information:"
echo "  PostgreSQL:"
echo "    Host: localhost:5432"
echo "    Database: gokeys_test"
echo "    User: gokeys"
echo "    Password: gokeys_test_password"
echo "    Connection: postgres://gokeys:gokeys_test_password@localhost:5432/gokeys_test"
echo ""
echo "  Valkey (Redis-compatible):"
echo "    Host: localhost:6379"
echo "    Password: gokeys_test_password"
echo "    Connection: valkey://localhost:6379"
echo ""
echo "🧪 Ready to run tests:"
echo "  make test-handlers           # Run handler tests"
echo "  make test-handlers-integration  # Run integration tests"
echo "  make test-with-deps          # Run all tests with dependencies"
echo ""
echo "🛠️  Management commands:"
echo "  make test-deps-status        # Check service status"
echo "  make test-deps-logs          # View service logs"
echo "  make test-deps-down          # Stop services"
echo "  make test-deps-clean         # Stop and remove all data"
echo ""
echo "🌐 Optional pgAdmin:"
echo "  make test-deps-admin         # Start pgAdmin at http://localhost:8080"
echo ""
