#!/bin/bash

# Stop Test Environment Script
# This script stops PostgreSQL and Valkey test containers

set -e

echo "🛑 Stopping GoKeys Test Environment..."
echo ""

# Check if Dock<PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running."
    exit 1
fi

# Stop services
echo "📦 Stopping PostgreSQL and Valkey containers..."
docker-compose -f docker-compose.test.yml down

echo ""
echo "✅ Test environment stopped!"
echo ""
echo "🧹 To completely clean up (remove volumes):"
echo "  make test-deps-clean"
echo ""
echo "🚀 To start again:"
echo "  make test-deps-up"
echo "  ./scripts/start-test-env.sh"
echo ""
