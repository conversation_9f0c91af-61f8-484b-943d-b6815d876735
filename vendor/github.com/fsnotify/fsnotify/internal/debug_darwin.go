package internal

import "golang.org/x/sys/unix"

var names = []struct {
	n string
	m uint32
}{
	{"NOTE_ABSOLUTE", unix.NOTE_ABSOLUTE},
	{"NOTE_ATTRIB", unix.NOTE_ATTRIB},
	{"NOTE_BACKGROUND", unix.NOTE_BACKGROUND},
	{"NOTE_CHILD", unix.NOTE_CHILD},
	{"NOTE_CRITICAL", unix.NOTE_CRITICAL},
	{"NOTE_DELETE", unix.NOTE_DELETE},
	{"NOTE_EXEC", unix.NOTE_EXEC},
	{"NOTE_EXIT", unix.NOTE_EXIT},
	{"NOTE_EXITSTATUS", unix.NOTE_EXITSTATUS},
	{"NOTE_EXIT_CSERROR", unix.NOTE_EXIT_CSERROR},
	{"NOTE_EXIT_DECRYPTFAIL", unix.NOTE_EXIT_DECRYPTFAIL},
	{"NOTE_EXIT_DETAIL", unix.NOTE_EXIT_DETAIL},
	{"NOTE_EXIT_DETAIL_MASK", unix.NOTE_EXIT_DETAIL_MASK},
	{"NOTE_EXIT_MEMORY", unix.NOTE_EXIT_MEMORY},
	{"NOTE_EXIT_REPARENTED", unix.NOTE_EXIT_REPARENTED},
	{"NOTE_EXTEND", unix.NOTE_EXTEND},
	{"NOTE_FFAND", unix.NOTE_FFAND},
	{"NOTE_FFCOPY", unix.NOTE_FFCOPY},
	{"NOTE_FFCTRLMASK", unix.NOTE_FFCTRLMASK},
	{"NOTE_FFLAGSMASK", unix.NOTE_FFLAGSMASK},
	{"NOTE_FFNOP", unix.NOTE_FFNOP},
	{"NOTE_FFOR", unix.NOTE_FFOR},
	{"NOTE_FORK", unix.NOTE_FORK},
	{"NOTE_FUNLOCK", unix.NOTE_FUNLOCK},
	{"NOTE_LEEWAY", unix.NOTE_LEEWAY},
	{"NOTE_LINK", unix.NOTE_LINK},
	{"NOTE_LOWAT", unix.NOTE_LOWAT},
	{"NOTE_MACHTIME", unix.NOTE_MACHTIME},
	{"NOTE_MACH_CONTINUOUS_TIME", unix.NOTE_MACH_CONTINUOUS_TIME},
	{"NOTE_NONE", unix.NOTE_NONE},
	{"NOTE_NSECONDS", unix.NOTE_NSECONDS},
	{"NOTE_OOB", unix.NOTE_OOB},
	//{"NOTE_PCTRLMASK", unix.NOTE_PCTRLMASK}, -0x100000 (?!)
	{"NOTE_PDATAMASK", unix.NOTE_PDATAMASK},
	{"NOTE_REAP", unix.NOTE_REAP},
	{"NOTE_RENAME", unix.NOTE_RENAME},
	{"NOTE_REVOKE", unix.NOTE_REVOKE},
	{"NOTE_SECONDS", unix.NOTE_SECONDS},
	{"NOTE_SIGNAL", unix.NOTE_SIGNAL},
	{"NOTE_TRACK", unix.NOTE_TRACK},
	{"NOTE_TRACKERR", unix.NOTE_TRACKERR},
	{"NOTE_TRIGGER", unix.NOTE_TRIGGER},
	{"NOTE_USECONDS", unix.NOTE_USECONDS},
	{"NOTE_VM_ERROR", unix.NOTE_VM_ERROR},
	{"NOTE_VM_PRESSURE", unix.NOTE_VM_PRESSURE},
	{"NOTE_VM_PRESSURE_SUDDEN_TERMINATE", unix.NOTE_VM_PRESSURE_SUDDEN_TERMINATE},
	{"NOTE_VM_PRESSURE_TERMINATE", unix.NOTE_VM_PRESSURE_TERMINATE},
	{"NOTE_WRITE", unix.NOTE_WRITE},
}
